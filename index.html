<!doctype html>
<html lang="en" id="htmlRoot">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <title><%= VITE_GLOB_APP_TITLE %></title>
    <link rel="icon" href="/favicon.ico" />
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: '0a93e982b376824d33c84f11e8751db6',
      };
    </script>
    <script src="EasyPlayer-element.min.js"></script>
    <script
      type="text/javascript"
      src="https://webapi.amap.com/maps?v=2.0&key=8daa33fd0168182c44c29076ce995985&plugin=AMap.Geocoder&plugin=AMap.MouseTool&plugin=AMap.Autocomplete&plugin=AMap.PolyEditor&plugin=AMap.MarkerClusterer"
    ></script>

    <!-- UI组件库 1.0.11 -->
    
    <script src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script> 
    <script src="https://webapi.amap.com/loader.js"></script>
  </head>
  <body>
    <div id="app">
      <style>
        html[data-theme='dark'] .app-loading {
          background-color: #2c344a;
        }

        html[data-theme='dark'] .app-loading .app-loading-title {
          color: rgb(255 255 255 / 85%);
        }

        .app-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background-color: #f4f7f9;
        }

        .app-loading .app-loading-wrap {
          display: flex;
          position: absolute;
          top: 50%;
          left: 50%;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transform: translate3d(-50%, -50%, 0);
        }

        .app-loading .dots {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 98px;
        }

        .app-loading .app-loading-title {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 30px;
          color: rgb(0 0 0 / 85%);
          font-size: 30px;
        }

        .app-loading .app-loading-logo {
          display: block;
          width: 90px;
          margin: 0 auto;
          margin-bottom: 20px;
        }

        .dot {
          display: inline-block;
          position: relative;
          box-sizing: border-box;
          width: 48px;
          height: 48px;
          margin-top: 30px;
          transform: rotate(45deg);
          animation: ant-rotate 1.2s infinite linear;
          font-size: 32px;
        }

        .dot i {
          display: block;
          position: absolute;
          width: 20px;
          height: 20px;
          transform: scale(0.75);
          transform-origin: 50% 50%;
          animation: ant-spin-move 1s infinite linear alternate;
          border-radius: 100%;
          opacity: 0.3;
          background-color: #0065cc;
        }

        .dot i:nth-child(1) {
          top: 0;
          left: 0;
        }

        .dot i:nth-child(2) {
          top: 0;
          right: 0;
          animation-delay: 0.4s;
        }

        .dot i:nth-child(3) {
          right: 0;
          bottom: 0;
          animation-delay: 0.8s;
        }

        .dot i:nth-child(4) {
          bottom: 0;
          left: 0;
          animation-delay: 1.2s;
        }

        @keyframes ant-rotate {
          to {
            transform: rotate(405deg);
          }
        }

        @keyframes ant-rotate {
          to {
            transform: rotate(405deg);
          }
        }

        @keyframes ant-spin-move {
          to {
            opacity: 1;
          }
        }

        @keyframes ant-spin-move {
          to {
            opacity: 1;
          }
        }
      </style>
      <div class="app-loading">
        <div class="app-loading-wrap">
          <img src="/logo.png" class="app-loading-logo" alt="Logo" />
          <div class="app-loading-dots">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
          <div class="app-loading-title"><%= VITE_GLOB_APP_TITLE %></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
