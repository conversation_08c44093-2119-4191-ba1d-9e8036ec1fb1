<template>
  <div class="content">
    <Modal v-model:open="open" title="省流提示" @ok="goon" @cancel="stopAll" ok-text="继续看" cancel-text="不看了">
      <p>你已经看很久了，是否要继续观看视频?</p>
    </Modal>
    <div class="video_1" :class="videoUrlData.length == 1 ? 'video_1' : 'video_2'" v-for="(v, index) in videoUrlData"
      :key="index + '_video'">
      <video autoplay controls width="100%" height="100%" :id="'video' + v"></video>
    </div>
    <div class="btnbox">
      <a-button class="btn" type="primary" :disabled="!canClose" @click="stopAll()">全部关闭</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import flvjs from 'flv.js';
import { onMounted, ref } from 'vue';
import { Modal } from 'ant-design-vue';
import { useMessage } from '@/hooks/web/useMessage';
import { sendCommand, getvehDetail } from '@/api/vehicle/vehlist';

const { params } = useRoute();
console.log('vehId', params.id);
let timer30: any;
let timer60: any;
let videoUrlData = ref([] as any);
const { createMessage } = useMessage();
//videoUrlData = params.channelIds.split(',');
const open = ref<boolean>(false);
let canClose = ref<boolean>(false);
let playObj = ref({} as any);
let playerList = ref([] as any);
let player: any;
let errorMsg = ref('');


function getDetail() {
  getvehDetail(params.id).then(res => {
    playObj.value = {
      baseUrl: res.baseUrl,
      deviceSn: res.deviceSn,
      channelNumbers: res.channelNumbers
    }
    videoUrlData.value = res.channelNumbers

    playAll()

  })
}

function goon() {
  open.value = false
  //销毁定时器
  clearTimeout(timer30);
  clearTimeout(timer60);
  timer30 = null
  timer60 = null
  timer30 = setTimeout(() => {
    console.log('30秒循环提示')
    open.value = true
  }, 30000);
  timer60 = setTimeout(() => {
    console.log('60秒时间到，自动关机')
    stopAll() //自动关闭
  }, 60000);
}

//结束全部
function stopAll() {
  open.value = false
  //销毁定时器
  clearTimeout(timer30);
  clearTimeout(timer60);
  timer30 = null
  timer60 = null
  canClose.value = false
  if (playerList.value.length) {
    //发送停止拉流指令
    Array.from(videoUrlData.value).forEach((channel) => {
      stopChannel(channel)
    });
  }
}

function stopChannel(channel) {
  sendCommand("realtime-video-upload", playObj.value.deviceSn, {
    channelNo: channel,
    "commandType": 0
  }).then((res) => {
    playerList.value.forEach(player => {
      console.log('销毁播放器')
      //视频播放出来才能关闭
      player.pause()
      player.unload()
      player.detachMediaElement()
      player.destroy()//销毁播放器
      player = null
    })
    playerList.value = []

  }).catch(err => {
    stopChannel(channel)
  });
}

//播放全部通道
function playAll() {
  console.log('启动播放器')
  //销毁定时器
  clearTimeout(timer30);
  clearTimeout(timer60);
  timer30 = null
  timer60 = null
  if (flvjs.isSupported()) {
    //发送拉流指令
    Array.from(videoUrlData.value).forEach((channel) => {
      sendCommand("realtime-video-upload", playObj.value.deviceSn, {
        channelNo: channel
      }).then((res) => {
        if (res.success == true) {
          console.log('视频流地址~~~', `${playObj.value.baseUrl}0${playObj.value.deviceSn}-${channel}`)
          let videoElement = document.getElementById('video' + channel) as HTMLVideoElement;
          player = flvjs.createPlayer({
            type: 'flv', //= > 媒体类型 flv 或 mp4
            isLive: true, //= > 是否为直播流
            hasAudio: true, //= > 是否开启声音 注意：开启声音就不会自动播放啦
            url: `${playObj.value.baseUrl}0${playObj.value.deviceSn}-${channel}`, //= > 视频流地址
          });

          player.attachMediaElement(videoElement); //= > 绑DOM
          player.load();
          player.play();
          player.on('error', function () {
            if (errorMsg.value == '视频播放出错') {
              return
            } else {
              console.log('视频播放出错');
              createMessage.info('请稍后再试');
              errorMsg.value = '视频播放出错'
              stopAll()
            }



          });
          playerList.value.push(player)
          player.on(flvjs.Events.STATISTICS_INFO, function (res) {

            //console.log('STATISTICS_INFO', res.speed);
            //根据播放速度判断画面是否开始播放
            if (res.speed > 0) {
              if (timer30 && timer60) {
                //console.log('已设置定时器')
              } else {
                console.log('播放画面启动，开始设置定时器')
                canClose.value = true
                //视频开始播放成功后启用2个定时器，30秒后弹出提示，60秒后弹出提示
                timer30 = setTimeout(() => {
                  console.log('30秒循环提示')
                  open.value = true
                }, 30000);
                timer60 = setTimeout(() => {
                  console.log('60秒时间到，自动关机')
                  stopAll() //自动关闭
                }, 60000);

              }
            }
          });
          console.log('视频开始播放');

        } else {
          createMessage.info(res.message);
        }
      });
    });

  } else {
    console.log('flvjs不支持');
  }


}



onMounted(() => {
  getDetail()

});
</script>

<style scoped>
.content {
  overflow: hidden;
}

.btnbox {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
}

.btn {
  margin: 10px;
}

.video_1 {
  width: 100%;
  height: 700px;
  margin: 10px;

}

.video_2 {
  width: calc(50% - 10px);
  height: 380px;
  margin: 2px 5px;
  float: left;
}
</style>
