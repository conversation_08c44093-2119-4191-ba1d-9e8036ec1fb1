import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';
/**
 * @description: 供应商列表
 */
export function getVendorList(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/vendor/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建供应商
 */
export function addVendor(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/vendor`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑供应商
 */
export function editVendor(id, data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/vendor/edit/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}


/**
 * @description: 删除供应商
 */
export function removeVendor(id) {
  return request.delete({
    url: `${MicroServiceEnum.MONITOR}/vendor/${id}`,
    // permission: 'vendor-remove'
  });
}

/**
 * @description: 视频配置列表
 */
export function getvideoStreamSettingList(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/videoStreamSetting/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建视频配置
 */
export function addvideoStreamSetting(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/videoStreamSetting`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑视频配置
 */
export function editvideoStreamSetting(id, data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/videoStreamSetting/edit/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}


/**
 * @description: 删除视频配置
 */
export function removevideoStreamSetting(id) {
  return request.delete({
    url: `${MicroServiceEnum.MONITOR}/videoStreamSetting/${id}`,
    // permission: 'vendor-remove'
  });
}


/**
 * @description: 获取视频配置列表
 */
export function getvideoStreamOptions() {
  return request.get({
    url: `${MicroServiceEnum.MONITOR}/videoStreamSetting/option`,
  });
}
