<script lang="tsx" setup>
  import { BasicTable, useTable, BasicColumn, FormSchema } from '@/components/Table';
  import { getTotalstatistic, getDailyWorktime, getMonthlyWorktime } from '@/api/data/statis';
  import { getCategoryTree } from '@/api/data/category';
  import { ref, onMounted } from 'vue';
  // import { useGeoCoder } from '@/hooks/web/useAMap';
  import { ECharts } from '@/components/ECharts';
  import barOptions from '../chart/bar-worktime.option';
  import dayjs from 'dayjs';
  import { getOrgTreeOptions } from '@/api/passport/org';

  defineOptions({
    name: 'report:work-time',
  });

  // const { getAddress } = useGeoCoder();
  const columns: BasicColumn[] = [
    {
      title: '车架号',
      dataIndex: 'vin',
    },
    {
      title: '设备型号',
      dataIndex: 'model',
    },
    {
      title: '所属机构',
      dataIndex: 'orgName',
    },
    {
      title: '总工作小时',
      dataIndex: 'workHours',
    },
  ];
  const type = ref('month');
  const searchFormSchema: FormSchema[] = [
    {
      field: 'criteria.type',
      component: 'RadioButtonGroup',
      label: '',
      colProps: {
        span: 4,
      },
      defaultValue: 'month',
      componentProps: {
        options: [
          {
            label: '按月统计',
            value: 'month',
          },
          {
            label: '按日统计',
            value: 'day',
          },
        ],
        onChange: (e, v) => {
          console.log('RadioButtonGroup====>:', e, v);
          type.value = e;
        },
      },
    },
    {
      field: 'criteria.range',
      label: '日期范围',
      component: 'RangePicker',
      colProps: { span: 7 },
      defaultValue: [
        dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      componentProps: {
        style: 'width: 100%;',
        valueFormat: 'YYYY-MM-DD',
      },
      ifShow: () => type.value == 'day',
    },
    {
      field: 'criteria.range',
      label: '日期范围',
      component: 'RangePicker',
      colProps: { span: 7 },
      defaultValue: [
        dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      componentProps: {
        style: 'width: 100%;',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM-DD',
      },
      ifShow: () => type.value == 'month',
    },
    {
      field: 'criteria.modelBranch',
      label: '设备型号',
      component: 'ApiTreeSelect',
      colProps: { span: 7 },
      componentProps: {
        api: getCategoryTree,
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      field: 'criteria.orgBranch',
      label: '所属机构',
      component: 'ApiTreeSelect',
      colProps: { span: 6 },
      componentProps: {
        api: getOrgTreeOptions,
        labelField: 'name',
        valueField: 'uid',
      },
    },
    {
      field: 'criteria.keyword',
      label: 'VIN码',
      component: 'Input',
      colProps: { span: 6 },
    },
  ];

  const [registerTable] = useTable({
    api: getTotalstatistic,
    columns,
    maxHeight: 500,
    canResize: false,
    formConfig: {
      labelWidth: 90,
      schemas: searchFormSchema,
    },
    defSort: {
      order: {
        property: 'hours',
        direction: 'DESC',
      },
    },
    useSearchForm: true,
    showTableSetting: true,
    tableSetting: {
      export: {
        show: true,
        filename: '工作时间统计',
      },
    },
    showIndexColumn: false,
    beforeFetch: async (searchInfo: any) => {
      if (
        searchInfo.criteria.type == 'month' &&
        searchInfo.criteria.range &&
        searchInfo.criteria.range.length
      ) {
        searchInfo.criteria.range = [
          dayjs(searchInfo.criteria.range[0]).startOf('month').format('YYYY-MM-DD'),
          dayjs(searchInfo.criteria.range[1]).endOf('month').format('YYYY-MM-DD'),
        ];
      }
      let param = {
        ...searchInfo.criteria,
        begin: searchInfo.criteria.range[0],
        end: searchInfo.criteria.range[1],
      };
      initChart(searchInfo.criteria.type, param);
      if (searchInfo.criteria.range && searchInfo.criteria.range.length) {
        searchInfo.criteria.begin = searchInfo.criteria.range[0];
        searchInfo.criteria.end = searchInfo.criteria.range[1];
      } else {
        searchInfo.criteria.begin = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
        searchInfo.criteria.end = dayjs().format('YYYY-MM-DD');
      }
    },
    sortFn({ field, order }) {
      if (order) {
        return {
          order: {
            property: field,
            direction: order === 'ascend' ? 'ASC' : 'DESC',
          },
        };
      } else {
        return {
          order: {
            property: 'hours',
            direction: 'DESC',
          },
        };
      }
    },
  });

  const barChartOptions = ref<any>(barOptions);
  async function initChart(
    type: number,
    param: {
      orgBranch: any;
      categoryBranch: any;
      saleStatus: any;
      begin: any;
      end: any;
    },
  ) {
    const color = ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000'];
    let Data: any[] = [];
    // if (type == 1) {
    //   Data = await getMonthlyWorktime(param);
    // } else {
    //   Data = await getDailyWorktime(param);
    // }
    Data = await getDailyWorktime(param);
    const xAxis = Data.map((item) => {
      return item.todayDate;
    });
    const hours = Data.map((item) => {
      return item.workHours;
    });
    barChartOptions.value.xAxis.data = xAxis;
    barChartOptions.value.color = color;
    barChartOptions.value.series[0].data = hours;
  }

  //组件挂载时
  onMounted(() => {
    // let param = {
    //   orgBranch: null,
    //   categoryBranch: null,
    //   saleStatus: null,
    //   range: {
    //     begin: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    //     end: dayjs().format('YYYY-MM-DD'),
    //   }
    // }
    // initChart(2, param);
  });
</script>
<template>
  <BasicTable class="flex-1" @register="registerTable">
    <template #aboveOfTable>
      <div class="chartbox">
        <div style="width: 100%; height: 300px">
          <ECharts :options="barChartOptions" />
        </div>
      </div>
    </template>
  </BasicTable>
</template>
<style scoped>
  .chartbox {
    padding-bottom: 20px;
    //background-color: #fff;
  }
  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-picker .ant-picker-suffix) {
    color: #fff;
  }
</style>
