import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

export enum ScheduleStatus {
  // 执行中
  PENDING = 'PENDING',
  // 完成
  COMPLETE = 'COMPLETE',
  // 手动取消
  CANCEL = 'CANCEL',
  // 超时取消
  EXPIRE = 'EXPIRE',
}

/**
 * @description: 查询预约执行列表
 */
export function getScheduleList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/schedule/query`,
    data,
  });
}

/**
 * @description: 添加预约执行
 * @param equipUid 设备uid
 */
export function addSchedule(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/schedule`,
    data,
  });
}

/**
 * @description: 取消预约执行
 * @param equipUid 设备uid
 */
export function cancelSchedule(scheduleId) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/schedule/cancel/${scheduleId}`,
  });
}

/**
 * @description: 查询预约执行日志
 * @param equipUid 设备uid
 */
export function getScheduleCommandLogList(scheduleId) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/schedule/${scheduleId}/command-log`,
  });
}
