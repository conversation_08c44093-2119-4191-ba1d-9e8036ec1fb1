import { BasicColumn, FormSchema } from '@/components/Table';
import { getOrgTreeOptions } from '@/api/passport/org';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '机构名称',
    dataIndex: 'name',
    width: 160,
    align: 'left',
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 160,
    align: 'left',
    customRender: ({ record }) => {
      return h(
        'div',
        {},
        {
          default: () => (record.type == 'AGENT' ? '代理商' : '机构'),
        },
      );
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '机构名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'parentUid',
    label: '父机构',
    component: 'ApiTreeSelect',
    colProps: { span: 8 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  }
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '机构名称',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'parentUid',
    label: '父机构',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择',
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
    required: false,
  },
  {
    field: 'type',
    label: '机构类型',
    component: 'Select',
    defaultValue: null,
    required: true,
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '代理商', value: 'AGENT' },
        { label: '机构', value: 'ORG' },
      ],
    },
  },
];
