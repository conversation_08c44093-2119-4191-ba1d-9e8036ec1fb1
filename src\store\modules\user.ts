import type { IUserInfo } from '#/store';
import { defineStore } from 'pinia';
import { store } from '@/store';
import { PageEnum } from '@/enums/pageEnum';
import { TOKEN_KEY, USER_INFO_KEY, LOGIN_INFO_KEY } from '@/enums/cacheEnum';
import { getAuthCache, setAuthCache, removeAuthCache } from '@/utils/auth';
import type { LoginParams } from '@/api/sys/model/userModel';
import { loginApi,loginPsw } from '@/api/sys/user';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@/hooks/web/useMessage';
import { router } from '@/router';
import { usePermissionStore } from '@/store/modules/permission';
import { RouteRecordRaw } from 'vue-router';
import { PAGE_NOT_FOUND_ROUTE } from '@/router/routes/basic';
import { h } from 'vue';
import { getFirstPermissionMenuPath } from '@/router/helper/routeHelper';

interface UserState {
  userInfo: Nullable<IUserInfo>;
  token?: string;
  lastUpdateTime: number;
  loginInfo?: Nullable<LoginParams>;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // user info
    userInfo: null,
    // token
    token: undefined,
    // Last fetch time
    lastUpdateTime: 0,
    // user login info
    loginInfo: null,
  }),
  getters: {
    getUserInfo(): IUserInfo {
      return this.userInfo || getAuthCache<IUserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(TOKEN_KEY);
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
    getLoginInfo(): Nullable<LoginParams> {
      return this.loginInfo || getAuthCache<LoginParams>(LOGIN_INFO_KEY) || null;
    },
  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    removeToken() {
      removeAuthCache(TOKEN_KEY);
    },
    setUserInfo(info: IUserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setLoginInfo(loginInfo: LoginParams) {
      this.loginInfo = loginInfo;
      setAuthCache(LOGIN_INFO_KEY, loginInfo);
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
    },
    /**
     * @description: login
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
      },
    ): Promise<IUserInfo | null> {
      try {
        const { goHome = true, ...loginParams } = params;
        loginParams.ip = '127.0.0.1';
        const data = await loginPsw(loginParams);
        console.log('login data', data);
        const { object: userInfo } = data;

        if (userInfo) {
          const permissionStore = usePermissionStore();
          // save token
          this.setToken(userInfo.token);
          // set user info
          this.setUserInfo(userInfo);
          // set login info
          this.setLoginInfo(loginParams);
          permissionStore.setPermCodeList(userInfo.authorizations);
          this.afterLoginAction(goHome);
          return userInfo;
        } else {
          return Promise.reject(data);
        }
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async afterLoginAction(goHome?: boolean): Promise<void | null> {
      if (!this.getToken) return null;

      const permissionStore = usePermissionStore();
      if (!permissionStore.isDynamicAddedRoute) {
        const routes = await permissionStore.buildRoutesAction();
        routes.forEach((route) => {
          router.addRoute(route as unknown as RouteRecordRaw);
        });
        router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);
        permissionStore.setDynamicAddedRoute(true);

        if (routes.length) {
          const permissionRoutes = routes.filter(
            (route) => !['PageNotFound', 'ErrorLog'].includes(route.name),
          );
          const firstPermissionMenu = getFirstPermissionMenuPath(permissionRoutes);
          goHome && (await router.replace(firstPermissionMenu ?? PageEnum.BASE_HOME));
        } else {
          goHome && (await router.replace(PageEnum.BASE_HOME));
        }
      } else {
        goHome && (await router.replace(PageEnum.BASE_HOME));
      }
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false) {
      this.setToken(undefined);
      localStorage.removeItem('application');
      // this.setUserInfo(null)
      goLogin && router.push(PageEnum.BASE_LOGIN);
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: () => h('span', t('sys.app.logoutTip')),
        content: () => h('span', t('sys.app.logoutMessage')),
        onOk: async () => {
          await this.logout(true);
        },
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
