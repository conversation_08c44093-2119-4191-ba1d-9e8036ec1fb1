#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;
events {
    worker_connections 1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen 80;
        server_name  localhost;

        gzip_static on;

        gzip on;

        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;

        client_max_body_size 1024m;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root /usr/share/nginx/html;
            index index.html;
            autoindex on;

            if ($request_filename ~* .*.(gif|jpg|jpeg|png|woff|bmp|swf|js|css)$)
            {
                expires 30d;
            }

            if ($request_filename ~* .*.(html|htm|js)$)
            {
                expires -1s;
            }

            try_files $uri $uri/ /index.html;
        }

        location /api/ {
            proxy_read_timeout 1800;
            proxy_pass http://%PROXY%/;
            proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header    X-Forwarded-Proto $scheme;
            proxy_set_header    X-Forwarded-Port $server_port;
            proxy_hide_header WWW-Authenticate;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    # server {
    #     listen       443 ssl;
    #     server_name  xcmg.com;

    #     ssl_certificate      cert/4274539__xcmg.com.pem;
    #     ssl_certificate_key  cert/4274539__xcmg.com.key;

    #     ssl_session_cache    shared:SSL:1m;
    #     ssl_session_timeout  5m;

    #     ssl_ciphers  HIGH:!aNULL:!MD5;
    #     ssl_prefer_server_ciphers  on;

    #     location / {
    #         root   /usr/share/nginx/html;
    #         index  index.html index.htm;
    #     }

    #     error_page   500 502 503 504  /50x.html;
    #     location = /50x.html {
    #         root   html;
    #     }
    # }
}
