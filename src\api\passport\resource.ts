import { defHttp as request } from '@/utils/http/axios';
import { IResource } from './types';
import { useUserStoreWithOut } from '@/store/modules/user';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 获取可用的菜单
 */
export function getMenuList() {
  const userStore = useUserStoreWithOut();
  return getAuthorizedResources(userStore.getUserInfo.uid);
}

/**
 * @description: 获取可授权的菜单
 */
export function getAssignableMenuList() {
  const userStore = useUserStoreWithOut();
  return getAssignableResources(userStore.getUserInfo.uid);
}

/**
 * @description: 获取可用的菜单
 */
export function getAuthorizedResources(memberUid) {
  return request.get<IResource[]>({
    url: `/resource/assignable/${memberUid}`,
  });
}

/**
 * @description: 获取当前用户可授权的菜单
 */
export function getAssignableResources(memberUid) {
  return request.get({
    url: `/resource/assignable/${memberUid}`,
  });
}

/**
 * @description: 新建菜单
 */
export function addResources(data) {
  return request.post({
    url: `/resource`,
    data,
    // permission: 'resource-add'
  });
}

/**
 * @description: 编辑菜单
 */
export function editResources(id, data) {
  return request.put({
    url: `/resource/${id}`,
    data,
    // permission: 'resource-edit'
  });
}

/**
 * @description: 删除菜单
 */
export function removeResources(id) {
  return request.delete({
    url: `/resource/${id}`,
    // permission: 'resource-remove'
  });
}
