import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 机构列表
 */
export function getOrgList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/org/query`,
    data,
    // permission: 'org-add'
  });
}

/**
 * @description: 新建机构
 */
export function addOrg(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/org`,
    data,
    // permission: 'org-add'
  });
}

/**
 * @description: 编辑机构
 */
export function editOrg(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/org/${id}`,
    data,
    // permission: 'org-edit'
  });
}

/**
 * @description: 删除机构
 */
export function removeOrg(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/org/${id}`,
  });
}

/**
 * @description: 机构表单数据
 */
export function getOrgDetail(uid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/org/${uid}`,
  });
}

/**
 * @description: 用户机构树
 */
export function getOrgTree(kind: string) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/org/${kind.toLowerCase()}/tree`,
  });
}

/**
 * @description: 用户机构类型
 */
export function getOrgKindOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/org/kind/options`,
  });
}
