/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-24 15:42:34
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-27 15:09:54
 * @FilePath     : \special-front\src\api\data\command.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 查询所有命令
 */
export function getAllTerminal(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/terminal/queryTerminalAll`,
    params: data,
  });
}

/**
 * @description: 发送指令
 */
export function setTerminal(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/terminal/setTerminal`,
    params: data,
  });
}

/**
 * @description: 锁车解锁
 */
export function setLock(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/terminal/lock`,
    params: data,
  });
}

/**
 * @description: IC卡采集
 */
export function setRfid(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/rfid/setRfid`,
    params: data,
  });
}
/**
 * @description: IC卡下发
 */
export function setServerRfid(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/rfid/serverSetRfid`,
    params: data,
  });
}
/**
 * @description: 指纹采集
 */
export function setFinger(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/finger/setFinger`,
    params: data,
  });
}
/**
 * @description: 指纹下发
 */
export function setServerFinger(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/finger/serverSetFinger`,
    params: data,
  });
}
/**
 * @description: 人脸采集
 */
export function setFace(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/face/setFace`,
    params: data,
  });
}

/**
 * @description: 人脸下发
 */
export function setServerFace(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/face/serverSetFace`,
    params: data,
  });
}

/**
 * @description: 删除IC卡设备
 */
export function delRfid(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/rfid/deleteDriver`,
    params: data,
  });
}

/**
 * @description: 删除指纹设备
 */
export function delFinger(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/finger/deleteFinger`,
    params: data,
  });
}

/**
 * @description: 删除人脸设备
 */
export function delFace(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/face/deleteFace`,
    params: data,
  });
}

/**
 * @description: 查询设备IC卡
 */
export function queryDriverByRfid(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/rfid/queryDriver`,
    params: data,
  });
}

/**
 * @description: 查询设备指纹
 */
export function queryDriverByFinger(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/finger/queryDriver`,
    params: data,
  });
}

/**
 * @description: 查询设备人脸
 */
export function queryDriverByFace(data) {
  return defHttp.get({
    url: `${MicroServiceEnum.GW}/face/queryDriver`,
    params: data,
  });
}
/**
 * @description: 设备列表
 */
export function getCommandList(data) {
  return defHttp.post({
    url: `/command/query`,
    data,
  });
}

/**
 * @description: 新建控制命令
 */
export function addCommand(categoryId, data) {
  return defHttp.post({
    url: `/command/${categoryId}`,
    data,
  });
}

/**
 * @description: 编辑控制命令
 */
export function editCommand(categoryId, id, data) {
  return defHttp.put({
    url: `/command/${categoryId}/${id}`,
    data,
  });
}

/**
 * @description: 删除控制命令
 */
export function removeCommand(categoryId, id) {
  return defHttp.delete({
    url: `/command/${categoryId}/${id}`,
  });
}

/**
 * @description: 控制命令表单数据
 */
export function getCommandDetail(id) {
  return defHttp.get({
    url: `/command/${id}`,
  });
}

/**
 * @description: 控制命令类型选项
 */
export function getCommandKindOptions() {
  return defHttp.get({
    url: `/command/kind/options`,
  });
}

/**
 * @description: 控制命令预约触发条件选项
 */
export function getCommandOptions(categoryId, protocol) {
  return defHttp.get({
    url: `/command/${categoryId}/${protocol}`,
  });
}

/**
 * @description: 刷新控制命令结果
 */
export function refreshCommandLog(logId) {
  return defHttp.get(
    {
      url: `/command/log/${logId}`,
    },
    {
      errorMessageMode: 'none',
      successMessageMode: 'none',
    },
  );
}

/**
 * @description: 查询控制命令日志
 */
export function getCommandLogList(data) {
  return defHttp.post({
    url: `/command/log/query`,
    data,
  });
}

/**
 * @description: 复制控制命令
 */
export function copyCategoryCommand(categoryId, commandIds) {
  return defHttp.post({
    url: `/command/${categoryId}/copy`,
    data: commandIds,
  });
}
