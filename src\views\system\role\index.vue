<template>
  <div class="h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增角色 </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
            {
              icon: 'clarity:clone-line',
              onClick: handleAuthorize.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <RoleFormModal @register="registerFormModal" @success="handleSuccess" />
    <AuthorizeDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getRoleList, removeRole } from '@/api/passport/role';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import RoleFormModal from './RoleFormModal.vue';

  import { useDrawer } from '@/components/Drawer';
  import AuthorizeDrawer from './AuthorizeDrawer.vue';

  import { columns, searchFormSchema } from './role.data';

  export default defineComponent({
    name: 'RoleList',
    components: { BasicTable, TableAction, RoleFormModal, AuthorizeDrawer },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: '角色列表',
        api: getRoleList,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: undefined,
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerDrawer, { openDrawer }] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeRole(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleAuthorize(record: Recordable) {
        console.log(record);
        openDrawer(true, {
          record,
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handleAuthorize,
        registerFormModal,
        registerDrawer,
      };
    },
  });
</script>
