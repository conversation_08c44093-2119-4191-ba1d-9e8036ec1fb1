import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 设备列表
 */
export function getEquipList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/equip/query`,
    data,
  });
}

/**
 * @description: 添加设备
 */
export function addEquip(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/equip`,
    data,
  });
}

/**
 * @description: 编辑设备
 */
export function editEquip(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/equip/${id}`,
    data,
  });
}

/**
 * @description: 设备表单数据
 */
export function getEquipDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/${id}`,
  });
}

/**
 * @description: 设备绑定终端
 */
export function equipBindTerminal(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/equip/bind`,
    data,
  });
}

/**
 * @description: 设备换绑终端
 */
export function equipReplaceTerminal(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/equip/replace`,
    data,
  });
}

/**
 * @description: 设备解绑终端
 */
export function equipUnbindTerminal(equipId) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/equip/${equipId}/unbind`,
  });
}

/**
 * @description: VIN码自动补全
 */
export function getVinSuggest(keyword) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/auto-complete/${keyword}`,
  });
}

/**
 * @description: 设备解绑终端
 */
export function getTboxSuggest(keyword) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/tbox-auto-complete/${keyword}`,
  });
}

/**
 * @description: 设备状态列表
 */
export function getEquipStatusList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/equip/status`,
    data,
  });
}

/**
 * @description: 设备在线状态选项
 */
export function getEquipOnlineOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/online/options`,
  });
}

/**
 * @description: 设备在线状态选项
 */
export function getSingleEquipInfo(equipUid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/${equipUid}/overview`,
  });
}

/**
 * @description: 设备工况分组
 */
export function getMetricGroups(equipUid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/${equipUid}/metric`,
  });
}

/**
 * @description: 设备工况分组工况值
 */
export function getMetricGroupsData(equipUid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/${equipUid}/metric/values`,
  });
}

/**
 * @description: 首页大屏统计数据
 */
export function getDashboardStatData() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/equip/aggregate`,
  });
}
