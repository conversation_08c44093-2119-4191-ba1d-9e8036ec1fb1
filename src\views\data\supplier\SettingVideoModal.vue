<script lang="ts" setup name="member-sign-in-modal">
  import type { BasicColumn } from '@/components/Table';
  import { BasicTable, useTable } from '@/components/Table';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { getvideoStreamSettingList } from '@/api/data/supplier';
  import { ref } from 'vue';
  import { omit } from 'lodash-es';

  // register modal
  // const [registerModal, { setModalProps }] = useModalInner(async () => {
  //   setModalProps({ confirmLoading: false })
  // })
  const vendorId = ref<any>();
  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    console.log('data.record', data.record);
    vendorId.value = data.record.id;
    reload();
  });
  const signInLogColumns: BasicColumn[] = [
    {
      title: '基础路径',
      dataIndex: 'baseUrl',
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '供应商',
      dataIndex: 'vendorName',
    },
    {
      title: 'secret',
      dataIndex: 'secret',
    },
    {
      title: 'secretKey',
      dataIndex: 'secretKey',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
  ];
  const [registerTable, { reload }] = useTable({
    api: getvideoStreamSettingList,
    tableSetting: {
      size: false,
    },
    canResize: false,
    rowKey: 'id',
    columns: signInLogColumns,
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    beforeFetch: async (searchInfo: any) => {
      console.log('before fetch : ', searchInfo);
      if (!searchInfo) return;
      if (!searchInfo.criteria) searchInfo.criteria = {};
      const info = omit(searchInfo, ['pageIndex', 'pageSize', 'criteria', 'order']);
      for (const key of Object.keys(info)) {
        searchInfo.criteria[key] = info[key];
        delete searchInfo[key];
      }
      console.log(vendorId.value);
      searchInfo.criteria.vendorId = vendorId.value;
      // searchInfo.criteria.application = route.query.application
      console.log('searchInfo : ', JSON.stringify(searchInfo));
      return searchInfo;
    },
    fetchSetting: {
      listField: 'data',
    },
  });
</script>

<template>
  <BasicModal v-bind="$attrs" title="视频配置" @register="registerModal">
    <template #footer></template>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增配置 </a-button>
      </template>
    </BasicTable>
  </BasicModal>
</template>
