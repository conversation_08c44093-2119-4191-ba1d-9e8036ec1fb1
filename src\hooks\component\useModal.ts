import { inject, getCurrentInstance } from 'vue';
import type { DynamicModalInstance, DynamicModalOptions } from '@/components/Modal/src/typing';

export const DynamicModalSymbol = Symbol();

export interface ModalServiceContext {
  open: (content: any, options?: DynamicModalOptions) => DynamicModalInstance;
  closeAll: () => void;
}

export function useDynamicModal(): ModalServiceContext {
  if (!getCurrentInstance()) {
    throw new Error('useModal() can only be used inside setup() or functional components!');
  }

  const modalService = inject<ModalServiceContext>(DynamicModalSymbol);

  if (!modalService) {
    throw new Error('No dynamic modal context provided!');
  }

  return modalService;
}
