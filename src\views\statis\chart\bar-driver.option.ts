/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-10-09 17:28:56
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-09 17:29:07
 * @FilePath     : \special-front\src\views\statis\chart\bar-worktime.option copy.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import * as echarts from 'echarts';

export default {
  tooltip: {
    trigger: 'axis',
    show: true,
    axisPointer: {
      // 坐标轴指示器，坐标轴触发有效
      type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
    },
  },
  legend: {
    show: true,
    textStyle: {
      color: '#fff',
    },
  },
  grid: {
    top: '12%',
    left: '3%',
    right: '5%',
    bottom: '0%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      lineStyle: {
        color: '#fff',
      },
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '总工作时间',
      axisTick: {
        lineStyle: {
          color: '#fff',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#fff',
        },
      },
      axisLabel: {
        color: '#fff',
      },
    },
  ],
  series: [
    {
      name: '总工作时间',
      type: 'bar',
      barMaxWidth: 20,
      itemStyle: {
        barBorderRadius: [10, 10, 0, 0], // 圆角（左上、右上、右下、左下）
        color: new echarts.graphic.LinearGradient(
          1,
          0,
          0,
          0,
          [
            {
              offset: 0,
              color: '#51C5FD',
            },
            {
              offset: 1,
              color: '#005BB1',
            },
          ],
          false,
        ), // 渐变
      },
      data: [],
    },
    {
      name: '报警次数',
      type: 'bar',
      barMaxWidth: 20,
      itemStyle: {
        barBorderRadius: [10, 10, 0, 0], // 圆角（左上、右上、右下、左下）
        color: new echarts.graphic.LinearGradient(
          1,
          0,
          0,
          0,
          [
            {
              offset: 0,
              color: '#51C5FD',
            },
            {
              offset: 1,
              color: '#005BB1',
            },
          ],
          false,
        ), // 渐变
      },
      data: [],
    },
    {
      name: '总里程',
      type: 'line',
      barMaxWidth: 20,
      itemStyle: {
        barBorderRadius: [10, 10, 0, 0], // 圆角（左上、右上、右下、左下）
        color: new echarts.graphic.LinearGradient(
          1,
          0,
          0,
          0,
          [
            {
              offset: 0,
              color: '#51C5FD',
            },
            {
              offset: 1,
              color: '#005BB1',
            },
          ],
          false,
        ), // 渐变
      },
      data: [],
    },
  ],
};
