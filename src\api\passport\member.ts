/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-18 16:05:02
 * @FilePath     : \tzlink-gps-web\src\api\passport\member.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 编辑用户信息
 */
export function getMemberList(data) {
  return defHttp.post({
    url: `/application/member/query`,
    data,
    // permission: 'member-edit'
  });
}

/**
 * @description: 新建用户
 */
export function registerMember(data) {
  return defHttp.post({
    url: `/application/member/register`,
    data,
    // permission: 'member-add'
  });
}

/**
 * @description: 编辑用户信息
 */
export function editMember(id, data) {
  return defHttp.put({
    url: `/application/member/${id}`,
    data,
    // permission: 'member-edit'
  });
}

/**
 * @description: 删除用户
 */
export function removeMember(id) {
  return defHttp.delete({
    url: `/application/member/${id}`,
    // permission: 'member-edit'
  });
}

/**
 * @description: 修改密码
 */
export function changePassword(data) {
  return defHttp.post({
    url: `/application/member/change-password`,
    data,
    // permission: 'NONE'
  });
}

/**
 * @description: 重置密码
 */
export function resetPassword(data) {
  return defHttp.put({
    url: `/application/member/reset-password`,
    data,
    // permission: 'member-resetpassword'
  });
}

/**
 * @description: 登录日志列表
 */
export function getSignInLogList(data) {
  // const userStore = useUserStoreWithOut()
  console.log(data);
  return defHttp.get({
    url: `/application/member/${data.criteria.cuid.value}/signin/${data.pageIndex}`,
    // permission: 'member-resetpassword'
  });
}
