import { defHttp } from '@/utils/http/axios';


export function getLiftList(data) {
  return defHttp.post({
    url: `/special-backend/lift/query`,
    data,
  });
}

export function getHistoryVideo(data) {
  return defHttp.post({
    url: `/special-backend/lift/historyVideo`,
    data,
  });
}

export function getLiftinfo(id) {
  return defHttp.get({
    url: `/special-backend/lift/${id}`,
  });
}

export function getLiftvideo(id) {
  return defHttp.get({
    url: `/special-backend/lift/realVideo/${id}`,
  });
}

export function getLiftworktime(id, start, end) {
  return defHttp.get({
    url: `/special-backend/runStatistical/${id}/${start}/${end}`,
  });
}

export function getLiftAlarm(data) {
  return defHttp.post({
    url: `/special-backend/alarm/query`,
    data,
  });
}
export function getAlarminfo(id) {
  return defHttp.get({
    url: `/special-backend/alarm/detail/${id}`,
  });
}

export function getTerminalinfo(id) {
  return defHttp.get({
    url: `/special-backend/lift/terminal/${id}`,
  });
}

export function getTopdata(data) {
  return defHttp.post({
    url: `/special-backend/geojson/topData`,
    data,
  });
}

export function geojson(data) {
  return defHttp.post({
    url: `/special-backend/geojson/query`,
    data,
  });
}

export function queryAllLift(data) {
  return defHttp.post({
    url: `/special-backend/geojson/queryAll`,
    data,
  });
}

export function getYSYToken() {
  return defHttp.post({
    url: `/special-backend/lift/ysy/accessToken`,
  });
}