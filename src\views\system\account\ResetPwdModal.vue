<template>
  <BasicModal
    v-bind="$attrs"
    title="重置密码"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form/index'
import { resetFormSchema } from './account.data'
import { resetPassword } from '@/api/passport/member'
import { useUserStoreWithOut } from '@/store/modules/user'

export default defineComponent({
  name: 'ResetPwdModal',
  components: { BasicModal, BasicForm },
  emits: ['success', 'register'],
  setup(_, { emit }) {
    const rowId = ref<number | undefined>(undefined)
    const cuid = ref<any>()

    const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
      labelWidth: 120,
      schemas: resetFormSchema,
      showActionButtonGroup: false,
      actionColOptions: {
        span: 23
      }
    })

    const [registerModal, { setModalProps, closeModal }] = useModalInner(
      async data => {
        resetFields()
        setModalProps({ confirmLoading: false })
        console.log(data.record)
        rowId.value = data.record.id
        cuid.value = data.record.uid
        setFieldsValue({
          ...data.record
        })
      }
    )

    async function handleSubmit() {
      try {
        const values = await validate()
        setModalProps({ confirmLoading: true })
        // TODO custom api
        console.log(values)
        values.uid = cuid.value
        console.log(values)
        await resetPassword(values)
        closeModal()
        emit('success', {
          values: { ...values, id: rowId.value }
        })
      } finally {
        setModalProps({ confirmLoading: false })
      }
    }

    return { registerModal, registerForm, handleSubmit }
  }
})
</script>
