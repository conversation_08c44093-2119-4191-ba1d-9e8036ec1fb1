import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';

enum status {
  'NORMAL' = '正常',
  'SHUTDOWN' = '停机',
  'LOGOFF' = '注销',
}
export const columns: BasicColumn[] = [
  {
    title: 'sim卡号',
    dataIndex: 'simNo',
  },
  {
    title: 'ICCID号',
    dataIndex: 'iccid',
  },
  {
    title: 'IMSI号',
    dataIndex: 'imsi',
    width: 250,
  },
  {
    dataIndex: 'network',
    title: '网络类型',
  },
  {
    dataIndex: 'status',
    title: '卡状态',
    customRender: ({ record }) => {
      return h(
        'div',
        {},
        {
          default: () => status[record.status],
        },
      );
    },
  },
  {
    dataIndex: 'activateTime',
    title: '卡激活时间',
  },
  {
    title: '卡到期时间',
    dataIndex: 'expireTime',
  },
  {
    dataIndex: 'createTime',
    title: '注册时间',
  },
  {
    dataIndex: 'createMember',
    title: '注册人',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'criteria.simNo',
    label: 'sim卡号',
    component: 'Input',
    colProps: { span: 6 },
  }
];

const validateCard = async (_rule: Rule, value: string) => {
  if (value === '' || value.length !== 18) {
    return Promise.reject();
  } else {
    return Promise.resolve();
  }
};

export const formSchema: FormSchema[] = [
  {
    field: 'simNo',
    label: 'sim卡号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'iccid',
    label: 'ICCID号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'imsi',
    label: 'IMSI号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'network',
    label: '网络类型',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 12 },
    componentProps: {
      options: [
        { label: '2g', value: 0 },
        { label: '4g', value: 1 },
        { label: '5g', value: 2 },
      ],
    },
  },
  {
    field: 'status',
    label: '卡状态',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 12 },
    componentProps: {
      options: [
        { label: '正常', value: 'NORMAL' },
        { label: '停机', value: 'SHUTDOWN' },
        { label: '注销', value: 'LOGOFF' },
      ],
    },
  },
  {
    field: 'activateTime',
    label: '卡激活时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
  {
    field: 'expireTime',
    label: '卡到期时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
];
