<template>
  <div class="loginmain">
    <div class="loginboard">
      <h1><img src="@/assets/images/loginlogo.png" style="height: 39px;"/></h1>
      <div class="border-all">
        <!-- <ul>
          <li :class="{ choed: applicationActive == 'special-forklift' }" @click="changeLift('special-forklift')">叉车设备监控平台
          </li>
          <li :class="{ choed: applicationActive == 'special-lift' }" @click="changeLift('special-lift')">电梯物联网监控平台</li>
        </ul> -->
        <LoginForm />
      </div>
    </div>
    <!-- <div class="bottom"
      >江苏天泽智联 版权所有 | 苏ICP备xxxxxxxx号 | 技术支持：江苏天泽智联技术有限公司</div
    > -->
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import LoginForm from './LoginForm.vue';

defineProps({
  sessionTimeout: {
    type: Boolean,
  },
});
const applicationActive = ref('tzlink');
const changeLift = (application) => {
  applicationActive.value = application;
  localStorage.setItem('application', application);
};
changeLift(applicationActive.value);
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-login';
@logo-prefix-cls: ~'@{namespace}-app-logo';
@countdown-prefix-cls: ~'@{namespace}-countdown-input';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    background-color: @dark-bg;

    &::before {
      background-image: url('@/assets/svg/login-bg-dark.svg');
    }

    .ant-input,
    .ant-input-password {
      background-color: #232a3b;
    }

    .ant-btn:not(.ant-btn-link, .ant-btn-primary) {
      border: 1px solid #4a5569;
    }

    &-form {
      background: transparent !important;
    }

    .app-iconify {
      color: #fff;
    }
  }

  input.fix-auto-fill,
  .fix-auto-fill input {
    -webkit-text-fill-color: #c9d1d9 !important;
    box-shadow: inherit !important;
  }
}

.@{prefix-cls} {
  min-height: 100%;
  overflow: hidden;

  @media (max-width: @screen-xl) {
    background-color: #293146;

    .@{prefix-cls}-form {
      background-color: #fff;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin-left: -48%;
    background-image: url('@/assets/svg/login-bg.svg');
    background-repeat: no-repeat;
    background-position: 100%;
    background-size: auto 100%;

    @media (max-width: @screen-xl) {
      display: none;
    }
  }

  .@{logo-prefix-cls} {
    position: absolute;
    top: 12px;
    height: 30px;

    &__title {
      color: #fff;
      font-size: 16px;
    }

    img {
      width: 32px;
    }
  }

  .container {
    .@{logo-prefix-cls} {
      display: flex;
      width: 60%;
      height: 80px;

      &__title {
        color: #fff;
        font-size: 24px;
      }

      img {
        width: 48px;
      }
    }
  }

  &-sign-in-way {
    .anticon {
      color: #888;
      font-size: 22px;
      cursor: pointer;

      &:hover {
        color: @primary-color;
      }
    }
  }

  input:not([type='checkbox']) {
    min-width: 360px;

    @media (max-width: @screen-xl) {
      min-width: 320px;
    }

    @media (max-width: @screen-lg) {
      min-width: 260px;
    }

    @media (max-width: @screen-md) {
      min-width: 240px;
    }

    @media (max-width: @screen-sm) {
      min-width: 160px;
    }
  }

  .@{countdown-prefix-cls} input {
    min-width: unset;
  }

  .ant-divider-inner-text {
    color: @text-color-secondary;
    font-size: 12px;
  }
}
</style>
<style lang="less" scoped>
.loginmain {
  width: 100%;
  height: 100%;
  padding: 170px 0 0;
  background: url('@/assets/images/loginback.jpg') no-repeat;
  background-size: 100% 100%;
}

.loginmain h1 {
  padding: 17px 0 0 25px;
}

.loginmain h1 img {
  height: 80px;
}

.loginmid {
  position: relative;
  width: 100%;
}

.bkakd {
  width: 100%;
}

.loginboard {
  position: relative;
  width:425px;
  margin: 50px auto 0;
  padding: 20px 0;
  background: #fff;
  background-size: 100% 100%;
  height: 379px;

    h1{
      width: 100%;
      text-align: center;
      margin:0;
      padding: 20px 0 5px;
      font-weight: 700;
      color: #333;
      font-size: 30px;
    }

  .posd {
    position: absolute;
    top: -30px;
    left: 232px;

    img {
      width: 85px;
    }
  }

  .border-all {
    width: 100%;
    padding: 15px 16px 0;
    margin: 0;

    ul {
      display: flex;
      align-items: center;
      width: 100%;
    }

    li {
      width: calc(100% - 0);
      height: 40px;
      margin: 0 10px;
      border-radius: 5px;
      background: #fff;
      box-shadow: 0 0 10px #9cb1e6;
      color: #666;
      font-size: 15px;
      font-weight: 700;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
    }
    :deep(.ant-input){
      border-radius: 2px !important;
      height: 48px;
    }
    :deep(.ant-input-affix-wrapper){
      border-radius: 2px !important;
      height:48px;
    }
    :deep(.ant-input-affix-wrapper .ant-input){
   
      height: 33px;
    }
    :deep(.ant-btn.ant-btn-lg){
      height: 48px!important;
      border-radius: 2px;
    }
    .choed {
      background-color: #0a1d79;
      color: #fff;
    }
  }
}

.loginboard h2 {
  text-align: center;
}

.bottom {
  position: fixed;
  bottom: 25px;
  width: 100%;
  color: #fff;
  font-size: 14px;
  text-align: center;
}

.title_top {
  width: 100%;
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
}
</style>
