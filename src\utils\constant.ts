export const LIFT_VARIETIES = {
  TRACTION_AND_FORCE_DRIVE_ELEVATORS: "曳引与强制驱动电梯",
  TRACTION_DRIVE_LIFT_PASSENGER_LIFT: "曳引驱动电梯乘客电梯",
  SUNDRIES_ELEVATOR: "杂物电梯",
  HYDRAULICALLY_DRIVEN_ELEVATOR: "液压驱动电梯",
  ESCALATORS_AND_AUTOMATED_WALKWAYS: "自动扶梯与自动人形道",
};

export const LIFT_CONTRAL = {
  AC_TWO_SPEED: "交流双速",
  OTHER_CONTROL: "其他",
  FREQUENCY_CONVERSION: "变频",
  SET_TO_CHOOSE: "集选",
  PARALLEL_CONNECTION: "调压调速"
};

export const LIFT_PLACE = {
  TRAFFIC_PLACE: "交通场所",
  RESIDENTIAL: "住宅",
  OTHER_PLACES: "其他场所",
  OFFICE_AREA: "办公区域",
  HOSPITAL: "医院",
  SHOPPING_MALLS_SUPERMARKETS: "商场超市",
  SCHOOL: "学校",
  HOTEL: "宾馆酒店",
  ENTERTAINMENT: "文体娱乐场所",
};

export const LIFT_TYPE = {
  PASSENGER_ELEVATOR: "乘客电梯",
  FREIGHT_PASSENGER_ELEVATOR: "货客电梯",
  UTILITY_ELEVATOR: "杂物电梯",
  BED_ELEVATOR: "病床电梯",
  MOVING_SIDEWALK: "自动人行道",
  ESCALATOR: "自动扶梯",
  FREIGHT_ELEVATOR: "载货电梯",
  OTHER_ELEATOR: "其他",
};

export const FAULT_CODE = {
  0: "主动报警",
  1: "跌落报警",
  2: "碰撞报警",
  3: "语音报警",
  4: "安全回路报警",
  5: "困人报警",
  6: "低电量报警",
  7: "断电报警",
  9: "超速报警",
  10: "非门区停梯",
  11: "轿厢冲顶",
  12: "轿厢蹲底",
  13: "电动车识别报警",
  15: "意外移动",
  93: "下平层",
  94: "上平层",
  95: "开门行梯",
  96: "关门异常",
  97: "反复开关门",
  19: "有人挡梯",
  20: "有物挡梯",
  21: "煤气罐入梯",
  80: "楼层位置丢失",
  34: "非平层停梯",
  86: "其他阻止电梯再启动的故障",
  88: "电动机运转时间限制器动作",
  999: "自主申报",
  104: "安全回路 （高压）",
  102: "门锁回路",
  103: "抱闸线圈",
  101: "厅门回路",
  111: "检修",
  112: "锁梯",
  113: "上行减速",
  114: "下行减速",
  105: "冲顶",
  106: "蹲底",
  107: "开门极限",
  108: "关门极限",
  109: "意外移动",
  110: "安全回路 （低压）",



};