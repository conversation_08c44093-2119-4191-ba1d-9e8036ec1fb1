<template>
  <BasicDrawer
    v-bind="$attrs"
    show-footer
    :title="getTitle"
    width="50%"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './menu.data';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { useMessage } from '@/hooks/web/useMessage';

  import { addResources, editResources } from '@/api/passport/resource';

  export default defineComponent({
    name: 'MenuDrawer',
    components: { BasicDrawer, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const rowId = ref<number | undefined>(undefined);

      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 100,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: { lg: 12, md: 24 },
      });

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        resetFields();
        setDrawerProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;

        if (unref(isUpdate)) {
          setFieldsValue({
            ...data.record,
            iconCss: data.record.icon || null,
          });
          rowId.value = data?.record?.id;
        }
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '新增菜单' : '编辑菜单'));

      async function handleSubmit() {
        try {
          const values = await validate();
          setDrawerProps({ confirmLoading: true });
          // TODO custom api
          console.log(values);
          for (const key of Object.keys(values)) {
            if (values[key] === undefined) {
              values[key] = null;
            }
          }
          try {
            let res: any;
            if (unref(isUpdate)) {
              res = await editResources(rowId.value, values);
            } else {
              res = await addResources(values);
            }
            if (res.success) {
              closeDrawer();
              emit('success');
            } else {
              const { createMessage } = useMessage();
              createMessage.error(res?.message || '保存失败');
            }
          } catch (error) {
            console.log('error', error);
          }
        } finally {
          setDrawerProps({ confirmLoading: false });
        }
      }

      return { registerDrawer, registerForm, getTitle, handleSubmit };
    },
  });
</script>
