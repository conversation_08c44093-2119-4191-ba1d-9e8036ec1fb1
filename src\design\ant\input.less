@import (reference) '../color.less';

// input
.ant-input {
  &-number,
  &-number-group-wrapper {
    width: 100% !important;
    min-width: 110px;
    max-width: 100%;
  }
}

.ant-input-affix-wrapper .ant-input-suffix {
  right: 9px;

  &:focus{
    border:solid 1px #D9D9D9;
  }

  &:hover{
    border:solid 1px #D9D9D9;
  }
}

.ant-input-clear-icon {
  margin-right: 5px;
}

.ant-input-affix-wrapper-textarea-with-clear-btn {
  padding: 0 !important;

  textarea.ant-input {
    padding: 4px;
  }
}

:where(.css-dev-only-do-not-override-zu87i).ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover{
  border:solid 1px #D9D9D9;
  outline: none;
}

:where(.css-dev-only-do-not-override-zu87i).ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):focus{
  outline: none;
  box-shadow: 10px 10px #D9D9D9;
}

ant-input-affix-wrapper:focus{
  border:solid 1px #D9D9D9;
  outline: none;
}
