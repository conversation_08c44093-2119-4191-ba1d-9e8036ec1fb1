<template>
  <div class="home_div">
    <div class="left">
      <div class="top">
        <div class="scrollbox">
          <Tree
            style="width: 200px"
            v-if="List.length > 0"
            :defaultExpandAll="true"
            v-bind="selectedVeh"
            :fieldNames="fieldNames"
            :treeData="List"
            @select="select"
          >
            <template #title="{ dataRef }">
              <div class="tree_box">
                <Icon
                  icon="tabler:forklift"
                  v-if="dataRef.vin && dataRef.online !== '在线'"
                  style="margin-left: 5px; color: #ddd"
                />
                <Icon
                  icon="tabler:forklift"
                  v-if="dataRef.vin && dataRef.online == '在线'"
                  style="margin-left: 5px; color: #12ed80"
                />
                <div class="tree_box_name">{{ dataRef.name }}</div>
                <div
                  class="speed"
                  v-if="dataRef.vin && dataRef.gcj02Lat && dataRef.online == '在线'"
                >
                  {{ dataRef.gpsSpeed || '0.00' }} km/h
                  <EnvironmentOutlined />
                </div>
              </div>
            </template>
          </Tree>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="absolute top-3 left-70 z-9" style="top: 25px">
        <RangePicker v-model:value="dateRange" show-time :allow-clear="false" class="w-96" />
        <Button type="primary" @click="query" class="ml-4">查询</Button>
      </div>
      <div ref="mapRef" class="h-full"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { Dayjs } from 'dayjs';
  import { Tree, RangePicker, Button } from 'ant-design-vue';
  import { useAMap } from '@/hooks/web/useAMap';
  import dayjs from 'dayjs';
  import onlineImage from '@/assets/images/equip/gis-online.png';
  import offlineImage from '@/assets/images/equip/gis-offline.png';
  import pointImage from '@/assets/images/equip/trace.png';
  import { ref, onMounted } from 'vue';
  import { getvehicleList, queryTrack, getvehicleTree } from '@/api/vehicle/vehlist';
  import { PoweroffOutlined } from '@ant-design/icons-vue';
  import { EnvironmentOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '@/hooks/web/useMessage';

  const {
    mapRef,
    addMarker,
    addPolyline,
    getAddress,
    getMapInstance,
    createInfoWindow,
    setMapCenterFitView,
  } = useAMap(
    {
      center: [116.33, 39.9],
      zoom: 5,
      mapStyle: 'amap://styles/darkblue', // 使用深蓝色样式
    },
    initMap,
  );

  type RangeValue = [Dayjs, Dayjs];
  const dateFmt = 'YYYY-MM-DD HH:mm:ss';
  const dateRange = ref<RangeValue>([dayjs().subtract(1, 'hour'), dayjs()]);
  let infoWindow: AMap.InfoWindow | undefined;
  const fieldNames = {
    key: 'id',
    title: 'name',
  };
  const { createMessage } = useMessage();

  let selectedVeh = ref(null as any);

  type Point = AMap.LngLat;
  function initMap() {
    infoWindow = createInfoWindow();
  }

  function getTree() {
    getvehicleTree().then((res) => {
      List.value = transData(res);
    });
  }
  function transData(list) {
    list.forEach((v) => {
      v.name = v.name || v.vin;
      v.id = v.uid || v.id;
      v.children = transData([...(v.children || []), ...(v.equipGridViewViews || [])]);
    });
    return list;
  }
  function query() {
    console.log('query');
    if (!selectedVeh.value) return createMessage.warning('请选择设备');
    const [beginDate, endDate] = dateRange.value;
    const params = {
      begin: dayjs(beginDate.format(dateFmt)).format('YYYY-MM-DD HH:mm:ss'),
      end: dayjs(endDate.format(dateFmt)).format('YYYY-MM-DD HH:mm:ss'),
    };
    queryTrack(selectedVeh.value, params).then((res) => {
      console.log('query track : ', res);
      if (res?.length) {
        const points: Point[] = [];

        for (let i = 0; i < res.length; i++) {
          const item = res[i];
          let icon: AMap.Icon = new AMap.Icon({
            image: pointImage,
            size: [10, 13],
          });
          let addedMarker: AMap.Marker;
          const point = new AMap.LngLat(item.gcj02Lng, item.gcj02Lat);
          if (i === 0) {
            icon = new AMap.Icon({
              image: onlineImage,
              size: [37, 41],
            });
            addedMarker = addMarker(point, { icon, offset: [-18.5, -20], extData: item });
            // const carIcon = new AMap.Icon({
            //   image: carImage,
            //   size: [30, 30],
            //   imageSize: [30, 30],
            // });
            // // 起点额外添加小车图标
            // addMarker(point, { icon: carIcon, offset: [-15, -15] });
            // let center = [point.lat, point.lng];
            // setMapCenterFitView([point.lat, point.lng]);
          } else if (i === res.length - 1) {
            icon = new AMap.Icon({
              image: offlineImage,
              size: [37, 41],
            });
            addedMarker = addMarker(point, { icon, offset: [-18.5, -20], extData: item });
          } else {
            addedMarker = addMarker(point, { icon, offset: [-5, -6.5], extData: item });
          }

          addedMarker.on('click', async (e: any) => {
            console.log('extraData : ', e.target.getExtData, e);
            const extData = e.target.getExtData();
            console.log('extData : ', extData);
            let address = '--';
            try {
              address = await getAddress([e.lnglat.lng, e.lnglat.lat]);
              console.log('address');
            } catch (error) {
              console.log('get address error : ', error);
            }
            infoWindow?.setContent(`
                <div style="font-size:14px;margin-top: 6px;">Acc状态：${
                  extData.acc == 'Close' ? '关' : '开'
                }</div>
                <div style="font-size:14px;margin-top: 6px;">速度：${extData.speed}</div>
                <div style="font-size:14px;margin-top: 10px;">
                  时间：${extData.gpsTime || '--'}
                </div>
                <div style="font-size:14px;margin-top: 6px;">位置：${address}</div>
              `);
            infoWindow?.open(getMapInstance()!, [item.gcj02Lng, item.gcj02Lat]);
          });

          points.push(point);
        }

        //缩放地图中心
        setMapCenterFitView([points[0].lat, points[0].lng]);
        // 轨迹线
        addPolyline(points);
      }
    });
  }

  let List = ref([] as any);
  function onSearch(keword) {
    getvehicleList({
      pageIndex: 1,
      pageSize: 100,
      order: { property: 'id', direction: 'ASC' },
      criteria: { vehicleNo: keword },
    }).then((res) => {
      List.value = res.data;
    });
  }
  function select(id) {
    selectedVeh.value = id[0];
    console.log('id', id[0]);
    query();
  }

  onMounted(() => {
    const route = useRoute();
    if (route.query?.code) {
      selectedVeh.value = route.query?.code;
      query();
    }
    getTree();
  });
</script>

<style scoped lang="less">
  :deep(.ant-picker .ant-picker-input > input) {
    color: #fff;
  }
  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-picker .ant-picker-suffix) {
    color: #fff;
  }
  .home_div {
    display: flex;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: none;
  }

  .left {
    width: 250px;
    height: 100%;
  }

  .scrollbox {
    width: 100%;
    height: calc(100vh - 145px);
    max-height: calc(100vh - 145px);
    margin-top: 10px;
    padding-bottom: 100px;
    overflow-y: auto;
  }

  .top {
    padding: 10px;
  }

  .vehbox {
    display: flex;
    padding: 15px;
    border-bottom: solid 1px #536884;
    cursor: pointer;
  }

  .vehbox:hover {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .selected {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .right {
    flex: 1;
    height: 100%;
    padding: 10px;
    overflow: hidden;
  }

  .width_new {
    width: 100%;
    height: 100%;
  }
  .tree_box {
    display: flex;
    align-items: center;
    overflow: hidden;
    &_name {
      flex: 1;
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .speed {
      flex-shrink: 0;
      margin-left: 5px;
      color: #12ed80;
    }
  }
  /* 以下是marker点弹框的样式 */
</style>
