import type { ModalServiceContext } from '@/hooks/component/useModal';

import DynamicModalEmitter from '@/components/Modal/src/emitter';
import { DynamicModalSymbol } from '@/hooks/component/useModal';
import { markRaw } from 'vue';
import { uniqueId } from 'lodash-es';

import type { App, Plugin } from 'vue';
import type { DynamicModalInstance, DynamicModalOptions } from '@/components/Modal/src/typing';

export default {
  install: (app: App) => {
    const modalService = {
      open: (content: any, options?: DynamicModalOptions) => {
        const instance: DynamicModalInstance = {
          open: true,
          key: `dynamic_modal_instance_${uniqueId()}`,
          content: content && markRaw(content),
          options: options || {},
          data: options && options.data,
          close: (params) => {
            DynamicModalEmitter.emit('close', { instance, params });
          },
        };

        DynamicModalEmitter.emit('open', { instance });

        return instance;
      },
      closeAll: () => {
        DynamicModalEmitter.emit('close-all');
      },
    };

    // app.config.unwrapInjectedRef = true; // Remove it after Vue 3.3. Details: https://vuejs.org/guide/components/provide-inject.html#working-with-reactivity
    app.config.globalProperties.$modal = modalService;
    app.provide<ModalServiceContext>(DynamicModalSymbol, modalService);
  },
} as Plugin;
