import { IResource } from './types';

import { defHttp } from '@/utils/http/axios';
import { useUserStoreWithOut } from '@/store/modules/user';

/**
 * @description: 获取可用的菜单
 */
export function getMenuList() {
  const userStore = useUserStoreWithOut();
  return getAuthorizedResources(userStore.getUserInfo.uid);
}

// /**
//  * @description: 获取可授权的菜单
//  */
// export function getAssignableMenuList() {
//   const userStore = useUserStoreWithOut();
//   return getAssignableResources(userStore.getUserInfo.uid);
// }

/**
 * @description: 获取可授权的菜单
 */
export function getAssignableMenuList() {
  return defHttp.get<IResource[]>({
    url: `/resource/tree`,
  });
}

/**
 * @description: 获取可用的菜单
 */
export function getAuthorizedResources(memberUid) {
  return defHttp.get<IResource[]>({
    url: `/menu/authorized/${memberUid}`,
  });
}

/**
 * @description: 获取当前用户可授权的菜单
 */
export function getAssignableResources(memberUid) {
  return defHttp.get({
    url: `/resource/assignable/${memberUid}`,
  });
}

/**
 * @description: 新建菜单
 */
export function addResources(data) {
  return defHttp.post({
    url: `/menu`,
    data,
    // permission: 'resource-add'
  });
}

/**
 * @description: 编辑菜单
 */
export function editResources(id, data) {
  return defHttp.put({
    url: `/menu/${id}`,
    data,
    // permission: 'resource-edit'
  });
}

/**
 * @description: 删除菜单
 */
export function removeResources(id) {
  return defHttp.delete({
    url: `/menu/${id}`,
    // permission: 'resource-remove'
  });
}

/**
 * @description: 新建操作
 */
export function addOperation(data) {
  return defHttp.post({
    url: `/operation`,
    data,
  });
}

/**
 * @description: 编辑操作
 */
export function editOperation(id, data) {
  return defHttp.put({
    url: `/operation/${id}`,
    data,
  });
}

/**
 * @description: 删除操作
 */
export function removeOperation(id) {
  return defHttp.delete({
    url: `/operation/${id}`,
  });
}
