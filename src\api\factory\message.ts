import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 未读消息列表
 */
export function getUnreadMessageList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/message/unread`,
    data,
  });
}

/**
 * @description: 将消息置为已读
 */
export function setMessageRead(data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/message/read`,
    data,
  });
}
