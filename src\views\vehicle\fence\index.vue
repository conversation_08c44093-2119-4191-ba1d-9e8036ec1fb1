<template>
  <div class="flex flex-col h-full p-4">
    <BasicForm
      @register="registerForm"
      @submit="handleSubmit"
      @reset="handleReset"
      style="border-bottom: 1px solid #546985"
    />
    <div class="flex-1">
      <div class="topbar">
        <a-button type="primary" @click="handleCreate"> 新增 </a-button>
        <a-button @click="open"> 启用 </a-button>
        <a-button @click="stop"> 停用 </a-button>
      </div>

      <Modal v-model:open="isOp" title="提示" @ok="open" @cancel="!isOp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" size="30" />
          <p class="pt-3">是否启用选中围栏？</p>
        </div>
      </Modal>
      <Modal v-model:open="isSp" title="提示" @ok="stop" @cancel="!isSp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" />
          <p class="pt-3">是否停用选中围栏？</p>
        </div>
      </Modal>

      <!-- 围栏信息 -->
      <AddFormModal @register="registerModal" @success="handleReset" />
      <!-- 设备信息 -->
      <AuthCarListForm @register="registerCarModal" />
      <!-- 关联报警 -->
      <AlertListForm @register="registerAlertModal" />

      <CheckboxGroup v-model:value="checkList" class="flex flex-wrap">
        <div v-for="(item, index) in data" :key="index" class="item">
          <div class="topsjd">
            <Checkbox class="check" :value="item.id">
              {{ '' }}
            </Checkbox>
            <Dropdown :trigger="['click']" class="more" placement="bottomLeft">
              <template #overlay>
                <Menu>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="handleEdit(item)">修改</div>
                    </Badge>
                  </MenuItem>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="del(item.id)">删除</div>
                    </Badge>
                  </MenuItem>
                </Menu>
              </template>
              <a class="el-dropdown-link">
                <Icon icon="ri:more-line" style="color: #fff" />
              </a>
            </Dropdown>
          </div>
          <mapItem :key="'map-item-' + item.id" :info="item" class="item-top" />
          <div class="item-middle">
            <div style="display: flex; justify-content: space-between">
              <div style="font-size: 14px">{{ item.name }}</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 10px">
              <div style="font-size: 13px">
                <span class="hui">触发：</span>

                <span v-if="item.alertEventName == '驶入'" class="normal"> 驶入 </span>
                <span v-else-if="item.alertEventName == '驶出'" class="normal"> 驶出 </span>
              </div>
              <div style="font-size: 13px">
                <span class="hui">所属机构：</span>
                {{ item.orgName }}
              </div>
            </div>
          </div>
          <div class="item-end">
            <div class="hui">
              查看信息：
              <span
                style="color: #1890ff; cursor: pointer; padding: 3px 5px; font-size: 14px"
                @click="relVehicle({ item })"
              >
                {{ item.deviceCount }}
              </span>
            </div>
            <div class="hui">
              关联预警：
              <span
                style="color: #1890ff; cursor: pointer; padding: 3px 5px; font-size: 14px"
                @click="relAlarm(item)"
              >
                {{ item.alertCount }}
              </span>
            </div>
          </div>
        </div>
      </CheckboxGroup>
      <div class="flex justify-end m-2">
        <Pagination
          v-model:current="searchParams.pageIndex"
          v-model:page-size="searchParams.pageSize"
          :total="total"
          :show-total="(total) => `共 ${total} 条数据`"
          @show-size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import {
    CheckboxGroup,
    Checkbox,
    Dropdown,
    Menu,
    MenuItem,
    Badge,
    Modal,
    Pagination,
  } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import Icon from '/@/components/Icon/Icon.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getFenceList, removeFence, unUseFence, useFence } from '@/api/vehicle/fence';
  // import { useDrawer } from '@/components/Drawer';
  import { useModal } from '@/components/Modal';
  import { searchFormSchema } from './fence.data';
  import mapItem from './districtMap.vue';
  import AddFormModal from './AddFormModal.vue';
  import AuthCarListForm from './authCarListForm.vue';
  import AlertListForm from './alertListForm.vue';
  // import { LinkOutlined, AlertOutlined } from '@ant-design/icons-vue';
  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerCarModal, { openModal: openCarModal }] = useModal();
  const [registerAlertModal, { openModal: openAlertModal }] = useModal();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: searchFormSchema,
    showActionButtonGroup: true,
  });

  const isOp = ref(false);
  const isSp = ref(false);

  const data = ref([]);
  const checkList = ref([]);

  const total = ref(0);
  const searchParams = ref({
    column: 'createTime',
    order: {
      property: 'id',
      direction: 'DESC',
    },
    pageIndex: 1,
    pageSize: 10,
  });

  // 查询
  const handleSubmit = async () => {
    const form = await validate();
    const params = Object.assign({}, form, searchParams.value);
    const res = await getFenceList(params);
    data.value = res.data;
    total.value = res.total;
    searchParams.value.pageSize = res.pageSize;
  };

  // 重置
  const handleReset = async () => {
    setFieldsValue({});
    searchParams.value.pageIndex = 1;
    searchParams.value.pageSize = 10;
    await handleSubmit();
  };

  const handleSizeChange = (current, size) => {
    searchParams.value.pageIndex = 1;
    handleSubmit();
  };

  // 新增
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record) {
    // console.log('handleEdit', record);
    openModal(true, {
      isUpdate: true,
      record,
    });
  }

  // 启用
  async function open() {
    console.log('open', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await useFence(checkList.value);
    isOp.value = false;
    await handleSubmit();
  }

  // 停用
  async function stop() {
    console.log('stop', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await unUseFence(checkList.value);
    isSp.value = false;
    await handleSubmit();
  }

  async function del(id) {
    await removeFence(id);
    await handleSubmit();
  }

  function relVehicle(item) {
    console.log('relVehicle', item);
    openCarModal(true, {
      ...item,
    });
  }

  function relAlarm(item) {
    openAlertModal(true, {
      ...item,
    });
  }

  onMounted(() => {
    handleSubmit();
  });
</script>
<style lang="less" scoped>
  .topbar {
    margin-top: 10px;
    gap: 10px;
    display: flex;
    justify-content: flex-start;
  }

  .item {
    width: 306px;
    height: 262px;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-color: #ccc;
    border-radius: 2px;
    position: relative;
    margin-top: 10px;
    margin-right: 25px;
  }
  .item-top {
    width: 100%;
    height: 150px;
  }
  .item-middle {
    width: 100%;
    height: 70px;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding: 7px 10px;
    text-align: center;
  }
  .item-end {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 10px 2px;
    background: #f5f5f5;
    color: #666;
  }
  .check {
    float: left;
  }
  .topsjd {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.35);
    width: 100%;
    z-index: 99;
    padding: 2px 8px 5px;
    color: #fff;
  }
  .more {
    float: right;
    cursor: pointer;
  }
  .lan {
    width: 40px;
    height: 18px;
    background: #1890ff;
    line-height: 18px;
    color: white;
    text-align: center;
    border-radius: 2px;
  }
  .normal {
    width: 40px;
    height: 18px;
    background: #e6f7ff;
    line-height: 18px;
    color: #339900;
    text-align: center;
    border-radius: 2px;
    display: inline-block;
  }
  .hui {
    font-size: 13px;
    color: #666;
  }
</style>
