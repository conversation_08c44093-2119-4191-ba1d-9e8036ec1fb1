import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

export enum TboxTestStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  PASS = 'PASS',
}

/**
 * @description: 终端安装记录
 */
export function getTboxReplacementList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/tbox/replacement/query`,
    data,
  });
}

/**
 * @description: 终端调试列表
 */
export function getTboxTestList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/tbox/test/query`,
    data,
  });
}

/**
 * @description: 获取设备调试信息
 */
export function getTboxTestItems(equipUid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/tbox/test/${equipUid}`,
  });
}

/**
 * @description: 获取设备调试信息
 */
export function sendTboxTestCommand(equipUid, data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/tbox/test/${equipUid}/send`,
    data,
  });
}

/**
 * @description: 设备调试通过
 */
export function tboxTestPass(equipUid) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/tbox/test/${equipUid}/pass`,
  });
}

/**
 * @description: 设备调试重置
 */
export function tboxTestReset(equipUid) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/tbox/test/${equipUid}/reset`,
  });
}

/**
 * @description: 终端异常 -- 长期离线
 */
export function getTboxTroubleOffline(data) {
  const days = data?.criteria?.days;
  delete data?.criteria?.days;
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/tbox/trouble/offline/${days}`,
    data,
  });
}

/**
 * @description: 终端异常 -- 长期离线
 */
export function getTboxTroubleLocation(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/tbox/trouble/location`,
    data,
  });
}
