<template>
  <div class="home_div">
    <div class="left">
      <div class="top">
        <div class="title">
          <div class="tit">车辆列表</div>
          <RedoOutlined style="color: #bbc3ce" @click="refresh" />
        </div>
        <div class="org">
          <a-tree-select
            placeholder="请选择机构"
            allowClear
            treeDefaultExpandAll
            style="width: 100%"
            :treeData="orgOptions"
            @change="selectorg"
            :field-names="orgfieldNames"
          />
        </div>
        <div class="search">
          <a-input-search
            v-model:value="vin"
            placeholder="设备名称"
            enter-button
            @search="onSearch"
          />
        </div>
        <Tabs v-model:activeKey="activeKey" @change="stateChange">
          <TabPane key="1" :tab="'全部(' + VehData.total + ')'">
            <div class="scrollbox">
              <a-tree
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                @select="select"
                v-model:checkedKeys="carList"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online !== '在线'"
                      style="margin-left: 5px; color: #ddd"
                    />
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online == '在线'"
                      style="margin-left: 5px; color: #12ed80"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>

                    <div
                      class="speed"
                      v-if="dataRef.vin && dataRef.gcj02Lat && dataRef.online == '在线'"
                    >
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined/>
                    </div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
          <TabPane key="2" :tab="'在线(' + VehData.online + ')'">
            <div class="scrollbox">
              <a-tree
                style="width: 250px"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                v-model:checkedKeys="carList"
                @select="select"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online !== '在线'"
                      style="margin-left: 5px; color: #ddd"
                    />
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online == '在线'"
                      style="margin-left: 5px; color: #12ed80"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>

                    <div class="speed" v-if="dataRef.vin && dataRef.gcj02Lat">
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
          <TabPane key="3" :tab="'离线(' + VehData.offline + ')'">
            <div class="scrollbox">
              <a-tree
                style="width: 250px"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                v-model:checkedKeys="carList"
                @select="select"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online !== '在线'"
                      style="margin-left: 5px; color: #ddd"
                    />
                    <Icon
                      icon="tabler:forklift"
                      v-if="dataRef.vin && dataRef.online == '在线'"
                      style="margin-left: 5px; color: #12ed80"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <div class="right">
      <div class="topbox">
        <div class="mapcard">
              <div id="container" class="width_new">
                <div class="legend">
                  <div class="col">
                    <img class="icon" src="../../assets/images/online.png" />
                    在线
                  </div>
                  <div class="col">
                    <img class="icon" src="../../assets/images/offline.png" />
                    离线
                  </div>                  
                  <div class="col">
                    <img class="icon" src="../../assets/images/alarm.png" />
                    报警
                  </div>
                  <div class="col" style="margin-right;: 0">
                    <img class="icon" src="../../assets/images/nosig.png" />
                    无效定位
                  </div>
                </div>
              </div>
            </div>
      </div>

      <div class="table" :class="isup ? 'up' : 'down'">
        <div class="tabs">
          <Tabs v-model:activeKey="tab" @change="tablestateChange">
            <TabPane key="1">
              <template #tab>
                <span> 已选中 ({{ carTableList.length }})</span>
              </template>
            </TabPane>
            <TabPane key="2">
              <template #tab>
                <span> 超速报警 ({{ staticMap.speedNum }})</span>
              </template>
            </TabPane>
            <TabPane key="3">
              <template #tab>
                <span> 疲劳驾驶报警 ({{ staticMap.tiredNum }})</span>
              </template>
            </TabPane>
            <TabPane key="4">
              <template #tab>
                <span> 安全带报警 ({{ staticMap.beltNum }})</span>
              </template>
            </TabPane>
            <TabPane key="5">
              <template #tab>
                <span> 其他报警 ({{ staticMap.otherNum }})</span>
              </template>
            </TabPane>

            <TabPane key="6">
              <template #tab>
                <span> 行驶 ({{ staticMap.travelNum }})</span>
              </template>
            </TabPane>

            <TabPane key="7">
              <template #tab>
                <span> 静止 ({{ staticMap.staticlNum }})</span>
              </template>
            </TabPane>

            <TabPane key="8">
              <template #tab>
                <span> 异常 ({{ staticMap.errorNum }})</span>
              </template>
            </TabPane>

            <TabPane key="9">
              <template #tab>
                <span> 掉线 ({{ staticMap.offlineNum }})</span>
              </template>
            </TabPane>
          </Tabs>
          <Table
            v-if="!isup"
            :dataSource="carTableList"
            :columns="carColumns"
            :scroll="{ x: 2500, y: 500 }"
            :pagination="false"
          />
          <div class="arrow" @click="toggle">
            <UpOutlined v-if="isup" class="toggle_arrow" />
            <DownOutlined v-if="!isup" class="toggle_arrow" />
          </div>
        </div>
      </div>
    </div>

    <DicFormModal @register="registerDicFormModal" @success="handleCarSuccess" />
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, h, onDeactivated, watch, computed } from 'vue';
  import { ECharts } from '@/components/ECharts';
  import DicFormModal from './dicFormModal.vue';
  import {
    Tree,
    TreeSelect,
    Tabs,
    Button,
    Radio,
    Modal,
    Table,
    TableProps,
    Tag,
    Select,
    RangePicker,
    Spin,
    Pagination,
  } from 'ant-design-vue';
  import gisOnlinePng from '@/assets/images/online.png';
  import gisOffPng from '@/assets/images/offline.png';
  import nosigPng from '@/assets/images/nosig.png';
  import alarmPng from '@/assets/images/alarm.png';
  import {
    getvehicleTree,
    getlogresult,
    sendCommand,
    getVehData,
    getWorkdaily,
    getStaticData,
    getmileageData,
  } from '@/api/vehicle/vehlist';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import {
    RedoOutlined,
    EnvironmentOutlined,
    UpOutlined,
    DownOutlined,
  } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import type { Dayjs } from 'dayjs';
  import { useAMap } from '@/hooks/web/useAMap';
  // import onlineImage from '@/assets/images/equip/gis-online.png';
  // import offlineImage from '@/assets/images/equip/gis-offline.png';
  // import pointImage from '@/assets/images/equip/trace.png';
  import Icon from '@/components/Icon/Icon.vue';
  import { carColumns } from './index.data';
  import { useModal } from '@/components/Modal';
  import { setLock } from '@/api/data/command';
  // import { router } from '@/router';
  import { useRoute, useRouter } from 'vue-router';

  let distributionMap;

  const { createMessage } = useMessage();
  const geocoder = new AMap.Geocoder({
    city: '010', // 城市设为北京，默认：“全国”
    radius: 1000, // 范围，默认：500
  });
  export default defineComponent({
    name: 'Dashboard',
    components: {
      DicFormModal,
      UpOutlined,
      DownOutlined,
      RedoOutlined,
      EnvironmentOutlined,
      Icon,
      Button,
      Table,
      ATree: Tree,
      APagination: Pagination,
      ATreeSelect: TreeSelect,
      Tabs,
      TabPane: Tabs.TabPane,
      RadioGroup: Radio.Group,
      Modal,
      Select,
      RangePicker,
      Spin,
      ECharts,
      RadioButton: Radio.Button,
      Radio,
    },

    setup() {
      let infoWindow: AMap.InfoWindow | undefined;
      const route = useRoute();
      const router = useRouter();
      watch(
        () => route,
        () => {
          if (history.state?.code) {
            try {
              carList.value = [Number(history.state?.code)];
              carTableList.value = filterCar(List.value);
              setTimeout(() => {
                // 获取所有的图层
                var allOverlays = distributionMap.getAllOverlays();
                // 遍历所有图层，查找Marker并触发点击事件
                allOverlays.forEach((overlay) => {
                  if (overlay instanceof AMap.Marker) {
                    // 触发点击事件
                    overlay.emit('click', { target: overlay });
                  }
                });
              }, 3000);
            } catch (error) {
              console.log(error);
            }
          }
        },
        { deep: true },
      );

      const {
        mapRef,
        map,
        addMarker,
        addPolyline,
        getAddress,
        getMapInstance,
        createInfoWindow,
        setMapCenterFitView,
      } = useAMap(
        {
          center: [116.33, 39.9],
          zoom: 5,
        },
        initTrackMap,
      );

      watch(mapRef, (el) => {
        if (el) {
          map.value = new AMap.Map(el, {
            center: [116.33, 39.9],
            zoom: 5,
          });
        }
      });

      type Point = AMap.LngLat;

      let canClose = ref<boolean>(false);
      let playObj = ref({} as any);
      let value1 = ref('3');
      let value2 = ref('3');
      let errorMsg = ref('');

      let timer: any;
      let channelOptions = ref([] as any);
      let chartOptions = ref({} as any);

      let carList = ref([] as any);

      const carTableList = ref([]);

      const staticMap = ref({} as any);

      watch(
        () => carList.value,
        (value) => {
          carTableList.value = filterCar(List.value);
          isup.value = carTableList.value.length == 0;
          getDistribution();
          staticMap.value.tiredNum = carTableList.value.filter(
            (v: any) => v.tiredAlert == '报警',
          ).length;
          staticMap.value.speedNum = carTableList.value.filter(
            (v: any) => v.speedAler == '报警',
          ).length;
          staticMap.value.otherNum = carTableList.value.filter(
            (v: any) => v.otherAlert == '报警',
          ).length;
          staticMap.value.beltNum = carTableList.value.filter(
            (v: any) => v.beltAlert == '报警',
          ).length;
          staticMap.value.travelNum = carTableList.value.filter(
            (v: any) => Number(v.gpsSpeed || 0) > 0,
          ).length;
          staticMap.value.staticlNum = carTableList.value.filter(
            (v: any) => Number(v.gpsSpeed || 0) == 0 && v.online == '离线',
          ).length;
          staticMap.value.errorNum = carTableList.value.filter(
            (v: any) => !location || v.location == '无效定位',
          ).length;
          staticMap.value.offlineNum = carTableList.value.filter(
            (v: any) => !v.online || v.online == '离线',
          ).length;
        },
        { deep: true },
      );

      function filterCar(treeData: any, list: any = []) {
        treeData.forEach((v) => {
          if (carList.value.includes(v.id) && v.model) {
            list.push(v);
          }
          if (v.children && v.children.length > 0) {
            filterCar(v.children, list);
          }
        });
        return list.map((v) => ({ ...v, children: null }));
      }

      chartOptions.value = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true,
            areaStyle: {},
          },
        ],
      };

      const spinning = ref<boolean>(false);

      type RangeValue = [Dayjs, Dayjs];
      const dateRange = ref<RangeValue>([dayjs().subtract(30, 'minute'), dayjs()]);
      const dateFmt = 'YYYY-MM-DD HH:mm:ss';
      const trackdateRange = ref<RangeValue>([dayjs().startOf('day'), dayjs()]);

      let openone = ref<boolean>(false);
      let opentwo = ref<boolean>(false);
      let total = ref('15');
      let online = ref('2');
      let onlinestate = ref(null as any);

      let activeKey = ref('1');

      let cardData = ref({
        access: 0,
        active: 0,
        offline: 0,
        online: 0,
        totalMileage: 0,
      });

      let VehData = ref({
        total: 0,
        online: 0,
        offline: 0,
      });

      let tab = ref('1');
      let tabKey = ref('1');
      let totalMiliege = ref(0);
      let vehtableTotal = ref(0);
      let alarmtableTotal = ref(0);

      let everMiliege = ref(0 as any);
      let dayNumber = ref(3);

      let isup = ref(true);
      let isMap = ref(true);

      const fieldNames = {
        key: 'id',
        title: 'name',
        icon: renderIcon(),
      };
      const orgfieldNames = {
        label: 'name',
        value: 'uid',
      };

      let orgUid = ref('');
      let vin = ref('');
      let modelKey = ref('map');

      let List = ref([] as any);

      let dataSource = ref([] as any);
      let top10dataSource = ref([] as any);

      let orgOptions = ref([] as any);

      let selectedVeh = ref(null as any);

      const columns: TableProps['columns'] = [
        {
          title: '车牌号',
          dataIndex: 'vin',
          width: 120,
        },
        {
          title: '设备型号',
          dataIndex: 'vehicleModelName',
          width: 120,
        },
        {
          title: 'GPS时间',
          dataIndex: 'gpsTime',
          width: 120,
        },
        {
          title: '位置',
          dataIndex: 'gpsPosition',
          width: 300,
        },
        {
          title: '速度',
          dataIndex: 'speed',
          width: 80,
        },
        {
          title: '报警',
          dataIndex: 'alarmCount',
          width: 10,
        },
        {
          title: '状态',
          dataIndex: 'online',
          width: 80,
          customRender: ({ record }) => {
            return h(
              Tag,
              {
                color: record.online === 'Off' ? 'blue' : 'green',
              },
              {
                default: () => (record.online === 'Off' ? '离线' : '在线'),
              },
            );
          },
        },
      ];

      const columns2: TableProps['columns'] = [
        {
          title: '排名',
          dataIndex: 'number',
          customRender: ({ record }) => {
            return `第${record.number}名`;
          },
        },
        {
          title: '车牌号',
          dataIndex: 'vin',
        },
        {
          title: '里程',
          dataIndex: 'mileage',
        },
        {
          title: '环比',
          dataIndex: 'rate',
          customRender: ({ record }) => {
            return h(
              Tag,
              {
                color: record.chain >= 0 ? 'green' : 'red',
              },
              {
                default: () =>
                  record.chain >= 0
                    ? Number(record.chain).toFixed(1) + '% ↑'
                    : Number(record.chain).toFixed(1) + '% ↓',
              },
            );
          },
        },
      ];

      const alarmcolumns: TableProps['columns'] = [
        {
          title: '车牌号',
          dataIndex: 'vin',
        },
        {
          title: '报警大类',
          dataIndex: 'heading',
        },
        {
          title: '报警类型',
          dataIndex: 'alarmType',
        },
        // {
        //   title: '报警内容',
        //   dataIndex: 'content',

        // },
        // {
        //   title: '车速(km/h)',
        //   dataIndex: 'speed',
        // },
        {
          title: '报警位置',
          dataIndex: 'address',
        },
        {
          title: '开始时间',
          dataIndex: 'beginTime',
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
        },
      ];

      top10dataSource.value = [
        {
          number: 1,
          vin: '测试00001',
          milige: '9365.1',
          rate: '14.86%',
          up: true,
        },
        {
          number: 2,
          vin: '测试00001',
          milige: '9365.1',
          rate: '4.1%',
          up: false,
        },
      ];

      function setModel(num) {}

      function addClass(num) {
        if (num == 1) {
          return 'video_1';
        } else if (num == 4) {
          return 'video_2';
        } else {
          return 'video_3';
        }
      }

      function renderIcon() {
        return '哈哈';
      }

      function handleClick(e) {
        console.log(e.target.value);
        if (e.target.value == 'map') {
          isMap.value = true;
        } else {
          isMap.value = false;
        }
      }

      function getorg() {
        getOrgTreeOptions().then((res) => {
          orgOptions.value = res;
        });
      }

      function refresh() {
        getTree();
        getvehdata();
      }

      function toggle() {
        if (isup.value == false) {
          isup.value = true;
          // initMap();
        } else {
          isup.value = false;
        }
      }

      function getcardinfo() {
        getStaticData({
          orgUid: orgUid.value,
        }).then((res) => {
          cardData.value = res;
        });
      }

      function gettopten(beginTime, endTime) {
        getmileageData({
          beginTime,
          endTime,
        }).then((res) => {
          top10dataSource.value = res;
        });
      }

      function getMiliege(beginTime, endTime) {
        getWorkdaily({
          beginTime,
          endTime,
        }).then((res) => {
          let sum: any = 0;
          let xAxis: any = [];
          let mileage: any = [];
          res.forEach((item) => {
            xAxis.push(item.mileageDate);
            mileage.push(item.mileage);
            sum += item.mileage;
          });
          chartOptions.value.xAxis.data = xAxis;
          chartOptions.value.series[0].data = mileage;
          totalMiliege.value = sum;
          everMiliege.value = (sum / dayNumber.value).toFixed(1);
        });
      }

      function searchTopten(e) {
        console.log(e.target.value);
        let data = {
          beginTime: dayjs().subtract(e.target.value, 'day').format('YYYY-MM-DD'),
          endTime: dayjs().format('YYYY-MM-DD'),
        };
        dayNumber.value = Number(e.target.value);
        gettopten(data.beginTime, data.endTime);
      }

      function searchMiliege(e) {
        console.log(e.target.value);
        let data = {
          beginTime: dayjs().subtract(e.target.value, 'day').format('YYYY-MM-DD'),
          endTime: dayjs().format('YYYY-MM-DD'),
        };
        dayNumber.value = Number(e.target.value);
        getMiliege(data.beginTime, data.endTime);
      }

      function getvehdata() {
        getVehData({
          orgUid: orgUid.value,
          online: onlinestate.value,
        }).then((res) => {
          VehData.value = res;
        });
      }

      function getTree() {
        getvehicleTree({
          vin: vin.value,
          orgUid: orgUid.value,
          online: onlinestate.value,
        }).then((res) => {
          List.value = transData(res);
          carTableList.value = filterCar(List.value);
          console.log('TreeData:', List.value);
        });
      }
      function select(veh, e) {
        const data = e.node;
        selectedVeh.value = data.id;
        if (modelKey.value == 'map') {
          //如果是地图模式
          if (data.gcj02Lng && data.gcj02Lat) {
            distributionMap.setCenter([data.gcj02Lng, data.gcj02Lat]);
            distributionMap.setZoom(14);
          }
        }
      }
      function transData(list) {
        list.forEach((v) => {
          v.name = v.name || v.vin;
          v.id = v.uid || v.id;
          v.children = transData([...(v.children || []), ...(v.equipGridViewViews || [])]);
        });
        return list;
      }

      function stopChannel(channel) {
        sendCommand('realtime-video-upload-control', playObj.value.deviceSn, {
          channelNo: channel,
          commandType: 0,
        })
          .then((res) => {
            console.log('查询下发结果');
            if (res.success == true) {
              console.log('查询下发结果');
              getResult(res.object);
            }
          })
          .catch((err) => {
            stopChannel(channel);
          });
      }
      //查询指令结果
      function getResult(id) {
        getlogresult(id).then((result) => {
          if (result.replyStatus == '等待应答') {
            timer = setTimeout(() => {
              getResult(id);
            }, 2000);
          } else if (result.replyStatus == '应答结束' && result.resultStatus == '成功') {
            timer = null;
            clearTimeout(timer);
          }
        });
      }

      function handleChange(e) {
        console.log('e~~~~~~~~~~~~~~~', e);
      }

      function initTrackMap() {
        infoWindow = createInfoWindow();
      }

      function initMap() {
        distributionMap = new AMap.Map('container', {
          //设置地图容器id
          viewMode: '2D', //是否为3D地图模式
          zoom: 8, //初始化地图级别
          mapStyle: 'amap://styles/normal', // 使用标准样式
          center: [117.20413, 34.16125], //初始化地图中心点位置
        });
        AMap.plugin(['AMap.ToolBar'], function () {
          // 缩放
          distributionMap.addControl(
            new AMap.ToolBar({
              position: {
                top: '160px',
                right: '40px',
              },
            }),
          );
        });
        AMap.plugin(['AMap.ControlBar'], function () {
          // 旋转
          distributionMap.addControl(
            new AMap.ControlBar({
              position: {
                top: '20px',
                right: '150px',
              },
            }),
          );
        });
        AMap.plugin(['AMap.HawkEye'], function () {
          // 小窗
          distributionMap.addControl(new AMap.HawkEye({}));
        });

        AMap.plugin(['AMap.MapType'], function () {
          // 卫星
          distributionMap.addControl(
            new AMap.MapType({
              defaultType: 0, // 0代表默认，1代表卫星
            }),
          );
        });

        getDistribution();
      }
      const selectElem = ref();
      function getDistribution() {
        // 清除地图上所有添加的覆盖物
        distributionMap.clearMap();
        carTableList.value.forEach((elem: any) => {
          if (elem.gcj02Lng != null && elem.gcj02Lat != null) {
            geocoder.getAddress(
              new AMap.LngLat(elem.gcj02Lng, elem.gcj02Lat),
              function (status, result) {
                if (status === 'complete' && result.regeocode) {
                  console.log('result.regeocode~~~', result.regeocode);
                  elem.gpsPosition = result.regeocode.formattedAddress;
                } else {
                  return (elem.gpsPosition = '');
                }
              },
            );
            let icon;
            if (elem.online == '在线') {
              icon = gisOnlinePng;
            } else {
              if (elem.Effective !== 'Effective') {
                icon = nosigPng;
              } else {
                icon = gisOffPng;
              }
            }
            if (elem.alarmCount > 0) {
              icon = alarmPng;
            }
            const marker = new AMap.Marker({
              map: distributionMap,
              position: [elem.gcj02Lng, elem.gcj02Lat], // 基点位置
              icon: new AMap.Icon({
                image: icon,
                size: new AMap.Size(50, 50), // 图标大小
                imageSize: new AMap.Size(50, 50),
              }),
              offset: new AMap.Pixel(0, -5), // 相对于基点的偏移位置
              extData: elem,
            });

            AMap.Event.addListener(marker, 'click', function (e) {
              console.log('点击maker~~~', e);
              const extData = e.target.getExtData();
              selectElem.value = extData;
              // 实例化信息窗体
              var content = `<div style="padding:6px;padding-left:10px;line-height:1.5;width:320px"> 
              车牌号：${extData.license} <br/>
              设备类型：${extData.model}<br/> 
              在线状态：${extData.online}<br/>
              当前速度：${extData.gpsSpeed || 0}km/h<br/> 
              当前位置：${extData.gpsPosition}<br/> 
              GPS时间：${extData.gpsTime}
              </div>
              <table style='width: 100%;margin-top:15px'>
                <tr>
                  <td style='text-align: center;width:25%;'>
                    <a class='goLocation' style='color:#67FAFE'>轨迹回放</a>
                  </td>
                  <td style='text-align: center;width:25%;'>
                    <a class='alarmBtn' style='color:#67FAFE'>报警历史</a>
                  </td>
                  <td style='text-align: center;width:25%'>
                    <a class='openDic' style='color:#67FAFE'>常用命令</a>
                  </td>
                  <td style='text-align: center;'>
                    ${extData.lockStatus == '未锁' ? "<a class='lockBtn' style='color:#67FAFE'>锁车</a>" : "<a class='unlockBtn' style='color:#67FAFE'>解锁</a>"}
                  </td></tr></table>
              `;

              const infoWindow = new AMap.InfoWindow({
                content: content,
                offset: new AMap.Pixel(25, -5),
              });

              infoWindow.open(distributionMap, marker.getPosition());
              AMap.Event.addListener(infoWindow, 'click', function (e) {
                const list = document.querySelectorAll('.openDic');
                list.forEach((el: any) => {
                  el.onclick = () => {
                    openDic();
                  };
                });
                const lockArr = document.querySelectorAll('.lockBtn');
                lockArr.forEach((el: any) => {
                  el.onclick = () => {
                    Modal.confirm({
                      title: '是否确认锁车?',
                      onOk: async () => {
                        const res = await setLock({
                          terminalId: selectElem.value.deviceSn,
                          type: 1,
                        });
                        createMessage.success(res?.json?.notice || '成功');
                        getTree();
                      },
                      onCancel() {
                        console.log('Cancel');
                      },
                    });
                  };
                });

                const unlockArr = document.querySelectorAll('.unlockBtn');
                unlockArr.forEach((el: any) => {
                  el.onclick = () => {
                    Modal.confirm({
                      title: '是否确认解锁?',
                      onOk: async () => {
                        const res = await setLock({
                          terminalId: selectElem.value.deviceSn,
                          type: 0,
                        });
                        createMessage.success(res?.json?.notice || '成功');
                        getTree();
                      },
                      onCancel() {
                        console.log('Cancel');
                      },
                    });
                  };
                });
                //轨迹回放
                const locationArr = document.querySelectorAll('.goLocation');
                locationArr.forEach((el: any) => {
                  el.onclick = () => {
                    router.push({
                      path: '/location/track/index',
                      query: { code: selectElem.value.id },
                    });
                  };
                });
                //报警历史
                const alarmBtnArr = document.querySelectorAll('.alarmBtn');
                alarmBtnArr.forEach((el: any) => {
                  el.onclick = () => {
                    router.push({
                      path: '/alarm/history/index',
                      state: { code: selectElem.value.vin },
                    });
                  };
                });
              });
            });
            distributionMap.setFitView();
          }
        });
      }
      const [registerDicFormModal, { openModal: openDicFormModal }] = useModal();

      function openDic() {
        openDicFormModal(true, {
          record: selectElem.value,
        });
      }
      function selectorg(e) {
        orgUid.value = e;
        getTree();
      }

      function stateChange(key) {
        console.log(key);
        if (key == '3') {
          onlinestate.value = 0;
        } else if (key == '2') {
          onlinestate.value = 1;
        } else {
          onlinestate.value = null;
        }
        getTree();
        getDistribution();
      }

      function tablestateChange(key) {
        let list = filterCar(List.value);

        switch (key) {
          case '1':
            carTableList.value = list;
            break;
          case '2':
            carTableList.value = list.filter((v: any) => v.speedAler == '报警');
            break;
          case '3':
            carTableList.value = list.filter((v: any) => v.tiredAlert == '报警');
            break;
          case '4':
            carTableList.value = list.filter((v: any) => v.beltAlert == '报警');
            break;
          case '5':
            carTableList.value = list.filter((v: any) => v.otherAlert == '报警');
            break;
          case '6':
            carTableList.value = list.filter((v: any) => Number(v.gpsSpeed || 0) > 0);
            break;
          case '7':
            carTableList.value = list.filter(
              (v: any) => Number(v.gpsSpeed || 0) == 0 && v.online == '离线',
            );
            break;
          case '8':
            carTableList.value = list.filter((v: any) => !location || v.location == '无效定位');
            break;
          case '9':
            carTableList.value = list.filter((v: any) => !v.online || v.online == '离线');
            break;
        }
      }

      //搜车
      function onSearch(keyword) {
        console.log(keyword);
        getTree();
        getDistribution();
      }

      function handleCarSuccess() {
        getTree();
      }

      onMounted(() => {
        gettopten(dayjs().subtract(3, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD'));
        getMiliege(dayjs().subtract(3, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD'));
        getTree();
        getorg();
        initTrackMap();
        initMap();
        getcardinfo();
        getvehdata();
      });
      return {
        registerDicFormModal,
        handleCarSuccess,
        select,
        carList,
        carTableList,
        staticMap,
        value1,
        value2,
        mapRef,
        total,
        online,
        dateFmt,
        activeKey,
        spinning,
        isMap,
        tab,
        modelKey,
        dateRange,
        tabKey,
        openone,
        opentwo,
        isup,
        dataSource,
        top10dataSource,
        columns,
        carColumns: carColumns as any,
        totalMiliege,
        dayNumber,
        everMiliege,
        columns2,
        alarmcolumns,
        vehtableTotal,
        VehData,
        alarmtableTotal,
        canClose,
        playObj,
        channelOptions,
        errorMsg,
        trackdateRange,
        orgOptions,
        fieldNames,
        orgfieldNames,
        orgUid,
        onlinestate,
        selectedVeh,
        List,
        vin,
        chartOptions,
        cardData,
        initMap,
        getvehdata,
        tablestateChange,
        initTrackMap,
        handleChange,
        setModel,
        handleClick,
        refresh,
        stateChange,
        getcardinfo,
        onSearch,
        toggle,
        getorg,
        getDistribution,
        selectorg,
        getTree,
        addClass,
        stopChannel,
        getMiliege,
        searchTopten,
        gettopten,
        searchMiliege,
      };
    },
  });
</script>

<style scoped lang="less">
  .home_div {
    position: relative;
    width:100%;
    height:100%;
    margin: 0;
    padding: 0;
    background-color: #fff;
  }
  .toggle_arrow {
    color: #fff;
    font-size: 16px;
    cursor: pointer;
  }
  .layout {
    display: flex;
    position: absolute;
    top: -36px;
    right: 10px;
    width: 120px;
  }

  .mapcard {
    display: flex;
    width: 100%;

    .width_new {
      position: relative;
      flex: 1;
      height:%;

      .legend {
        position: absolute;
        z-index: 999;
        top: 10px;
        left: 300px;
        padding: 0 0 0 5px;
        border-radius:2px;
        background-color: #FCFCFC;
        color: #000;
        border:solid 1px #ddd;

        .col {
          display: flex;
          margin: 5px 0;
          line-height: 19px;
          text-align: center;
          float: left; 
          margin-right: 15px;
          font-size: 13px;

          .icon {
            width: 19px;
            height: 19px;
            margin-right: 5px;
          }
        }
      }
    }

    .board {
      width: 420px;
      height: calc(100vh - 150px);
      margin-left: 10px;
      padding-bottom: 40px;
      overflow-y: auto;
      border-left: solid 1px #eee;
      background-color: #f8f8f8;

      .subtitle {
        color: #666;
      }

      .total {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .all {
          border-bottom: solid 1px #eee;

          .data {
            color: #1c96fa;
            font-size: 48px;
            font-weight: 500;
          }
        }

        .flex {
          text-align: center;

          .leftcard {
            flex: 1;
            height: 190px;
            margin-top: 10px;
            padding-top: 55px;

            .number {
              font-size: 36px;
              font-weight: 600;
            }
          }

          .righttcard {
            flex: 1;

            .col {
              margin: 5px 10px;
              border-bottom: solid 1px #eee;

              .number {
                padding-bottom: 5px;
                font-size: 30px;
                line-height: 1.1;
              }
            }
          }
        }
      }

      .miliege {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .data {
          margin-top: 12px;
          font-size: 34px;

          .subtitle {
            font-size: 24px;
          }
        }

        .chart {
          width: 100%;
          height: 300px;
        }

        .m_bottom {
          padding-top: 10px;
          border-top: solid 1px #eee;
          color: #666;
        }
      }

      .top10 {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .t_table {
          background-color: #fff;
        }
      }
    }
  }

  .middle {
    position: relative;
    height: 700px;
    overflow: hidden;

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .content {
      overflow: hidden;

      .v_list {
        width: 300px;
        float: left;
      }

      .video {
        width: 100%;
        height: calc(100vh - 350px);
      }

      .video_300 {
        width: calc(100% - 320px);
      }
    }

    .btn {
      margin: 10px;
    }
  }

  .track {
    width: 100%;
    height: calc(100vh - 150px);
    padding: 0;
    overflow: hidden;
  }

  .playbox {
    position: relative;
    height: 700px;
    margin: 0;
    overflow: hidden;

    .videobox {
      height: 650px;
      overflow: hidden;
    }

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .btn {
      margin: 10px;
    }
  }

  .video_1 {
    width: 100%;
    height: calc(100vh - 350px);
    margin: 0;
  }

  .video_2 {
    width: 48%;
    height: 280px;
    margin: 5px 1%;
    float: left;
  }

  .video_3 {
    width: 31%;
    height: 185px;
    margin: 5px 1%;
    float: left;
  }

  .left {
    width: 280px;
    height: calc(100% - 75px);
    border-right: none !important;
    position: absolute;
    z-index: 999;
    background: #fff;
    top:10px;
    left: 10px;
  }

  .tit {
    height: 18px;
    margin: 5px 0 0 5px;
    padding-left: 5px;
    border-left: solid 3px #0421bc;
    color: #0421bc;
    font-weight: bold;
    line-height: 18px;
  }

  .videolist {
    box-sizing: border-box;
    width: 300px;
    height: 36px;
    margin: 10px;
    padding: 0 5px;
    border-radius: 6px;
    background-color: #f2faff;
    box-shadow: 0 0 5px #ccc;
    line-height: 36px;
    cursor: pointer;
  }

  .scrollbox {
    width: 100%;
    max-height: calc(100vh - 280px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .top {
    padding: 10px;

    .title {
      display: flex;
      height: 30px;
      color: #666;
      font-size: 15px;
      font-weight: 600;
      line-height: 30px;

      .tit {
        flex: 1;
      }
    }

    .org {
      margin: 10px 0;
    }
  }

  .vehbox {
    display: flex;
    padding: 15px;
    border-bottom: solid 1px #eee;
    cursor: pointer;
  }

  .vehbox:hover {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .selected {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .right {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .topbox {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
    }

    .table {
      display: flex;
      position: absolute;
      z-index: 999;
      bottom: -10px;
      left: 0;
      width: calc(100% - 20px);
      margin-right: 10px;
      margin-left: 10px;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      background-color: rgb(0 0 0 / 30%);

      .tabs {
        position: relative;
        flex: 1;
        width: 100px;
        padding: 0 10px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background-color: #fff;
        
        .ant-tabs{
          margin-bottom: 10px
        }

        .arrow {
          position: absolute;
          top: 10px;
          right: 10px;
          width: 35px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0;
          background-color: #0421bc;
        }
      }
    }

    .up {
      bottom: 0;
    }

    .down {
      bottom: 0;
    }
  }

  .ant-picker-range {
    margin: 10px 0 0 10px;
  }
  .tree_box {
    display: flex;
    align-items: center;
    overflow: hidden;
    &_name {
      flex: 1;
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .speed {
      flex-shrink: 0;
      margin-left: 5px;
      color: #12ed80;
    }
  }
</style>