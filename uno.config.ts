import {
  defineConfig,
  presetTypography,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss';

export default defineConfig({
  shortcuts: [],
  theme: {
    colors: {
      antd: {
        primary: '#e60013',
        success: '#52c41a',
        error: '#ff4d4f',
        info: '#1677ff',
        warning: '#faad14',
      },
    },
  },
  presets: [presetUno(), presetTypography()],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  safelist: 'prose m-auto text-left'.split(' '),
});
