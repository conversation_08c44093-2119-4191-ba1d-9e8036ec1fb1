import { defHttp } from '@/utils/http/axios';
import { LoginParams, GetUserInfoModel } from './model/userModel';

import { ErrorMessageMode, BasicResponse } from '#/axios';
import { IUserInfo } from '#/store';

enum Api {
  Login = '/login',
  LoginPsw = '/auth/password',
  Logout = '/logout',
  GetUserInfo = '/getUserInfo',
  GetPermCode = '/getPermCode',
  TestRetry = '/testRetry',
  GetAuthorizedMenu = '/menu/authorized',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<BasicResponse<IUserInfo>>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export function loginPsw(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<BasicResponse<IUserInfo>>(
    {
      url: Api.LoginPsw,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(memberUid: string) {
  return defHttp.get<GetUserInfoModel>(
    { url: `${Api.GetAuthorizedMenu}/${memberUid}` },
    { errorMessageMode: 'none' },
  );
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.get({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  );
}
