[data-theme='dark'] ::-webkit-scrollbar-track {
  background-color: rgb(0 0 0 / 5%);
}

[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background-color: rgb(144 147 153 / 30%);
  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background-color: #b6b7b9;
}

[data-theme='dark'] #nprogress .bar {
  background-color: #0960bd;
}

[data-theme='dark'] .modal-icon-warning {
  color: #efbd47 !important;
}

[data-theme='dark'] .modal-icon-success {
  color: #55d187 !important;
}

[data-theme='dark'] .modal-icon-error {
  color: #ed6f6f !important;
}

[data-theme='dark'] .modal-icon-info {
  color: #0960bd !important;
}

[data-theme='dark'] .bg-white {
  background-color: #151515 !important;
}

[data-theme='dark'] html[data-theme='light'] .text-secondary {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] html[data-theme='light'] :not(:root):fullscreen::backdrop {
  background-color: #000 !important;
}

[data-theme='dark'] [data-theme='dark'] .text-secondary {
  color: #8b949e;
}

[data-theme='dark'] html {
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
}

[data-theme='dark'] body {
  background-color: #000;
  color: #c9d1d9;
}

[data-theme='dark'] h1,
[data-theme='dark'] h2,
[data-theme='dark'] h3,
[data-theme='dark'] h4,
[data-theme='dark'] h5,
[data-theme='dark'] h6 {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] a {
  background-color: transparent;
  color: #0960bd;
}

[data-theme='dark'] a:hover {
  color: #2a7dc9;
}

[data-theme='dark'] a:active {
  color: #004496;
}

[data-theme='dark'] a[disabled] {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] caption {
  color: #8b949e;
}

[data-theme='dark'] mark {
  background-color: #2c2712;
}

[data-theme='dark'] ::selection {
  background: #0960bd;
  color: #fff;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login {
  background-color: #293146;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login .ant-input,
[data-theme='dark'] html[data-theme='dark'] .vben-login .ant-input-password {
  background-color: #232a3b;
}

[data-theme='dark']
  html[data-theme='dark']
  .vben-login
  .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
  border: 1px solid #4a5569;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login-form {
  background: 0 0 !important;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login .app-iconify {
  color: #fff;
}

[data-theme='dark'] html[data-theme='dark'] .fix-auto-fill input,
[data-theme='dark'] html[data-theme='dark'] input.fix-auto-fill {
  -webkit-text-fill-color: #c9d1d9 !important;
}

@media (max-width: 1200px) {
  [data-theme='dark'] {
    background-color: #293146;
  }
}

[data-theme='dark'] .vben-login .vben-login-form {
  background-color: #fff;
}

[data-theme='dark'] .vben-login .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] .vben-login .container .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] .vben-login-sign-in-way .anticon {
  color: #888;
}

[data-theme='dark'] .vben-login-sign-in-way .anticon:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-login .ant-divider-inner-text {
  color: #8b949e;
}

[data-theme='dark'] .vben-app-logo.light {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .vben-app-logo.light .vben-app-logo__title {
  color: #0960bd;
}

[data-theme='dark'] .vben-app-logo.dark .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] html[data-theme='dark'] .vben-dark-switch {
  border: 1px solid #c4bcbc;
}

[data-theme='dark'] .vben-dark-switch {
  background-color: #151515;
}

[data-theme='dark'] .vben-dark-switch-inner {
  background-color: #fff;
}

[data-theme='dark'] .scroll-wrap {
  background-color: #151515;
}

[data-theme='dark'] .virtual-scroll-demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .virtual-scroll-demo__item {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .lazy-base-demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .form-wrap {
  background-color: #151515;
}

[data-theme='dark'] .full-loading {
  background-color: rgb(240 242 245 / 40%);
}

[data-theme='dark'] html[data-theme='dark'] .full-loading:not(.light) {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .full-loading.dark {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .step-form-content {
  background-color: #151515;
}

[data-theme='dark'] .desc-wrap {
  background-color: #151515 !important;
}

[data-theme='dark'] .result-success {
  background-color: #151515;
}

[data-theme='dark'] .result-success__content {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .result-error {
  background-color: #151515;
}

[data-theme='dark'] .result-error__content {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .result-error__content-icon {
  color: #ff4d4f;
}

[data-theme='dark'] .account-center-col:not(:last-child):not(:last-child) {
  border-right: 1px dashed rgb(206 206 206 / 50%);
}

[data-theme='dark'] .account-center-top {
  background-color: #151515;
}

[data-theme='dark'] .account-center-bottom {
  background-color: #151515;
}

[data-theme='dark'] .account-setting {
  background-color: #151515;
}

[data-theme='dark'] .account-setting .ant-tabs-tab-active {
  background-color: #111b26;
}

[data-theme='dark'] .list-card__card-title {
  color: #c9d1d9;
}

[data-theme='dark'] .list-card__card-detail {
  color: #8b949e;
}

[data-theme='dark'] .demo {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__top {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__top-col:not(:last-child) {
  border-right: 1px dashed #303030;
}

[data-theme='dark'] .list-basic__top-col div {
  color: #c9d1d9;
}

[data-theme='dark'] .list-basic__top-col p {
  color: #c9d1d9;
}

[data-theme='dark'] .list-basic__content {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__content .extra {
  color: #0960bd;
}

[data-theme='dark'] .list-search__container {
  background-color: #151515;
}

[data-theme='dark'] .list-search__content {
  color: #8b949e;
}

[data-theme='dark'] .list-search__action-item {
  color: #8b949e;
}

[data-theme='dark'] .list-search__action-item:first-child,
[data-theme='dark'] .list-search__action-item:nth-child(2) {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .list-search__time {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] span.iconify {
  background-color: #555;
}

[data-theme='dark'] .vben-default-layout {
  background-color: #f4f7f9;
}

[data-theme='dark'] .vben-basic-table-header-cell__help {
  color: rgb(0 0 0 / 65%) !important;
}

[data-theme='dark'] .step1 h3 {
  color: #c9d1d9;
}

[data-theme='dark'] .step1 h4 {
  color: #c9d1d9;
}

[data-theme='dark'] .step1 p {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-app-search-modal {
  background-color: rgb(0 0 0 / 25%);
}

[data-theme='dark'] .vben-app-search-modal-content {
  background-color: #151515;
}

[data-theme='dark'] .vben-app-search-modal-input {
  color: #1c1e21;
}

[data-theme='dark'] .vben-app-search-modal-input span[role='img'] {
  color: #999;
}

[data-theme='dark'] .vben-app-search-modal-cancel {
  color: #666;
}

[data-theme='dark'] .vben-app-search-modal-not-data {
  color: #969faf;
}

[data-theme='dark'] .vben-app-search-modal-list__item {
  background-color: #151515;
  box-shadow: 0 1px 3px 0 #d4d9e1;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-app-search-modal-list__item--active {
  background-color: #0960bd;
  color: #fff;
}

[data-theme='dark'] .account-center-application__card-num {
  color: #8b949e;
}

[data-theme='dark'] .account-center-application__card-download {
  color: #0960bd;
}

[data-theme='dark'] [data-theme='dark'] .vben-form-design-sider {
  background-color: #1f1f1f;
}

[data-theme='dark'] [data-theme='light'] .vben-form-design-sider {
  background-color: #fff;
}

[data-theme='dark'] .vben-st-login {
  background: #151515;
}

[data-theme='dark'] .vben-iframe-page__main {
  background-color: #151515;
}

[data-theme='dark'] .vben-lock-page__hour,
[data-theme='dark'] .vben-lock-page__minute {
  background-color: #141313;
  color: #bababa;
}

[data-theme='dark'] .vben-lock-page-entry {
  background-color: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .vben-lock-page-entry__header-name {
  color: #bababa;
}

[data-theme='dark'] .vben-lock-page-entry__err-msg {
  color: #ed6f6f;
}

[data-theme='dark'] ul li {
  border: 1px solid #ccc;
}

[data-theme='dark'] ul li:hover {
  border: 1px solid #0960bd;
  box-shadow: 0 2px 6px #0960bd;
  color: #0960bd;
}

[data-theme='dark'] .hidden-item {
  background-color: #f0bfc3;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .moving::before {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box:hover {
  background-color: rgb(9 96 189 / 20%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box::before {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box.active {
  background-color: rgb(9 96 189 / 20%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .show-key-box {
  color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .copy,
[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .delete {
  color: #fff;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .copy {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .delete {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box {
  background-color: rgb(152 103 247 / 12%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box .grid-row {
  background-color: rgb(152 103 247 / 12%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box .grid-row .grid-col .draggable-box {
  border: 1px #ccc;
}

[data-theme='dark']
  .draggable-box
  :deep(.list-main)
  .grid-box
  .grid-row
  .grid-col
  .draggable-box
  .list-main {
  border: 1px #ccc;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box::before {
  background: 0 0;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box.active {
  background-color: rgb(152 103 247 / 24%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box.active::before {
  background-color: #9867f7;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .copy,
[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .delete {
  color: #fff;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .copy {
  background-color: #9867f7;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .delete {
  background-color: #9867f7;
}

[data-theme='dark'] .form-panel .empty-text {
  color: #aaa;
}

[data-theme='dark'] .operating-area {
  border-bottom: 2px solid #ccc;
}

[data-theme='dark'] .operating-area a {
  color: #666;
}

[data-theme='dark'] .operating-area a.disabled,
[data-theme='dark'] .operating-area a.disabled:hover {
  color: #ccc;
}

[data-theme='dark'] .operating-area a:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-header {
  background-color: #fff;
  color: #fff;
}

[data-theme='dark'] .vben-layout-header-left .vben-layout-header-trigger.light:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header-left .vben-layout-header-trigger.light svg {
  fill: #000;
}

[data-theme='dark'] .vben-layout-header--light {
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  background-color: #fff !important;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-logo {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-logo:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action__item {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action__item:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action span[role='img'],
[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action-icon {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--dark {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .vben-layout-header--dark .vben-layout-header-action__item .ant-badge span {
  color: #fff;
}

[data-theme='dark'] .vben-setting-drawer-feature {
  background-color: #0960bd;
  color: #fff;
}

[data-theme='dark'] .vben-layout-footer {
  color: #d9d9d9;
}

[data-theme='dark'] .vben-layout-footer__links a {
  color: #d9d9d9;
}

[data-theme='dark'] .vben-layout-footer__links a:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-layout-footer__github:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-page-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-collapse-container {
  background-color: #151515;
}

[data-theme='dark'] .vben-collapse-container__header {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .vben-collapse-container__footer {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-page-wrapper-content-bg {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-help {
  color: #909399;
}

[data-theme='dark'] .vben-basic-help:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-title {
  color: #c9d1d9 !important;
}

[data-theme='dark'] .vben-basic-title-show-span::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-cropper-avatar-image-wrapper {
  border: 1px solid #303030;
  background: #151515;
}

[data-theme='dark'] .vben-cropper-avatar-image-mask {
  background: rgb(0 0 0 / 40%);
}

[data-theme='dark'] .vben-basic-table-row__striped td {
  background-color: #1e1e1e;
}

[data-theme='dark'] .vben-basic-table-form-container .ant-form {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-table .ant-table-wrapper {
  background-color: #151515;
}

[data-theme='dark'] .vben-tree {
  background-color: #151515;
}

[data-theme='dark'] .vben-tree-header {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .darg-verify {
  border: 1px solid #ddd;
  background-color: #eee;
}

[data-theme='dark'] .darg-verify-bar {
  background-color: #55d187;
}

[data-theme='dark'] .darg-verify-content.success {
  -webkit-text-fill-color: #fff;
}

[data-theme='dark'] .darg-verify-content > * {
  -webkit-text-fill-color: #333;
}

[data-theme='dark'] .darg-verify-action {
  background-color: #fff;
}

[data-theme='dark'] .ir-dv-img__tip {
  color: #fff;
}

[data-theme='dark'] .ir-dv-img__tip.success {
  background-color: rgb(85 209 135 / 60%);
}

[data-theme='dark'] .ir-dv-img__tip.error {
  background-color: rgb(237 111 111 / 60%);
}

[data-theme='dark'] .ir-dv-img__tip.normal {
  background-color: rgb(0 0 0 / 30%);
}

[data-theme='dark'] .vben-basic-drawer .ant-drawer-close:hover {
  color: #ed6f6f;
}

[data-theme='dark'] .vben-basic-drawer .ant-drawer-body {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-drawer__detail .ant-drawer-header {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-strength-meter-bar {
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .vben-strength-meter-bar::after,
[data-theme='dark'] .vben-strength-meter-bar::before {
  border-color: #fff;
  background-color: transparent;
}

[data-theme='dark'] .vben-strength-meter-bar--fill {
  background-color: transparent;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='0'] {
  background-color: #e74242;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='1'] {
  background-color: #ed6f6f;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='2'] {
  background-color: #efbd47;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='3'] {
  background-color: rgb(85 209 135 / 50%);
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='4'] {
  background-color: #55d187;
}

[data-theme='dark'] .vben-image-preview .ant-image-preview-operations {
  background-color: rgb(0 0 0 / 40%);
}

[data-theme='dark'] .vben-app-search-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
  color: #666;
}

[data-theme='dark'] html[data-theme='dark'] .vben-multiple-tabs .ant-tabs-tab {
  border-bottom: 1px solid #303030;
}

[data-theme='dark']
  html[data-theme='light']
  .vben-multiple-tabs
  .ant-tabs-tab:not(.ant-tabs-tab-active) {
  border: 1px solid #d9d9d9 !important;
}

[data-theme='dark'] .vben-multiple-tabs {
  border-bottom: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav {
  background-color: #151515;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  background-color: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab svg {
  fill: #c9d1d9;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab:not(.ant-tabs-tab-active):hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active {
  background: #0960bd;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab-active
  span {
  color: #fff !important;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab-active
  svg {
  fill: #fff;
}

[data-theme='dark'] .vben-multiple-tabs-content__extra-fold,
[data-theme='dark'] .vben-multiple-tabs-content__extra-quick,
[data-theme='dark'] .vben-multiple-tabs-content__extra-redo {
  border-left: 1px solid #303030;
  color: #8b949e;
}

[data-theme='dark'] .vben-multiple-tabs-content__extra-fold:hover,
[data-theme='dark'] .vben-multiple-tabs-content__extra-quick:hover,
[data-theme='dark'] .vben-multiple-tabs-content__extra-redo:hover {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-logo {
  border-bottom: 1px solid #eee;
}

[data-theme='dark'] .vben-layout-mix-sider.light.open > .scrollbar {
  border-right: 1px solid #eee;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-module__item {
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-module__item--active {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-menu-list__content {
  box-shadow: 0 0 4px 0 rgb(0 0 0 / 10%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-menu-list__title .pushpin {
  color: rgb(0 0 0 / 35%);
}

[data-theme='dark']
  .vben-layout-mix-sider.light
  .vben-layout-mix-sider-menu-list__title
  .pushpin:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-layout-mix-sider.dark .vben-layout-mix-sider-menu-list__title {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-module__item:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item--active {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item--active::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider-trigger {
  background-color: rgb(255 255 255 / 10%);
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-trigger {
  border-top: 1px solid #eee;
  background-color: #fff;
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list {
  background-color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title {
  border-bottom: 1px solid #eee;
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title .pushpin {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title .pushpin:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-inline,
[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-vertical,
[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-vertical-left {
  border-right: 1px solid transparent;
}

[data-theme='dark'] .vben-layout-mix-sider-drag-bar {
  background-color: #f8f8f9;
  box-shadow: 0 0 4px 0 rgb(28 36 56 / 15%);
}

[data-theme='dark'] .vben-layout-sideBar.ant-layout-sider-dark .ant-layout-sider-trigger {
  background-color: rgb(255 255 255 / 10%);
  color: #bfbfbf;
}

[data-theme='dark'] .vben-layout-sideBar.ant-layout-sider-dark .ant-layout-sider-trigger:hover {
  background-color: rgb(255 255 255 / 20%);
  color: #fff;
}

[data-theme='dark'] .vben-layout-sideBar:not(.ant-layout-sider-dark) .ant-layout-sider-trigger {
  border-top: 1px solid #303030;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-cropper-am-cropper {
  background: #eee;
}

[data-theme='dark'] .vben-cropper-am-preview {
  border: 1px solid #303030;
}

[data-theme='dark'] .vben-cropper-am-group {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-basic-drawer-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-drawer-header__back:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-modal-close span:first-child:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-modal-close span:last-child:hover {
  color: #ed6f6f;
}

[data-theme='dark'] html[data-theme='dark'] .lf-dnd {
  background: #080808;
}

[data-theme='dark'] .vben-flow-chart-toolbar {
  border-bottom: 1px solid #303030;
  background-color: #1e1e1e;
}

[data-theme='dark'] .vben-flow-chart-toolbar .disabeld {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .vben-flow-chart-toolbar__icon:hover {
  color: #0960bd;
}

[data-theme='dark'] .img-preview {
  background: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .img-preview-content {
  color: #fff;
}

[data-theme='dark'] .img-preview__close {
  background-color: rgb(0 0 0 / 50%);
  color: #fff;
}

[data-theme='dark'] .img-preview__close:hover {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .img-preview__index {
  background: rgb(109 109 109 / 60%);
}

[data-theme='dark'] .img-preview__controller {
  background: rgb(109 109 109 / 60%);
}

[data-theme='dark'] .img-preview__arrow {
  background-color: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .img-preview__arrow:hover {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .vben-darg-bar:hover {
  background-color: #0960bd;
  box-shadow: 0 0 4px 0 rgb(28 36 56 / 15%);
}

[data-theme='dark'] .vben-header-user-dropdown--light:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-header-user-dropdown--light .vben-header-user-dropdown__name {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-header-user-dropdown--light .vben-header-user-dropdown__desc {
  color: #7c8087;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link {
  color: #999;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link a {
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link a:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-separator {
  color: #999;
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link {
  color: rgb(255 255 255 / 60%);
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link a {
  color: rgb(255 255 255 / 80%);
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link a:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-separator,
[data-theme='dark'] .vben-layout-breadcrumb--dark .anticon {
  color: rgb(255 255 255 / 80%);
}

[data-theme='dark'] .file-table thead {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .file-table table,
[data-theme='dark'] .file-table td,
[data-theme='dark'] .file-table th {
  border: 1px solid #303030;
}

[data-theme='dark'] .context-menu {
  border: 1px solid rgb(0 0 0 / 8%);
  background-color: #151515;
  box-shadow:
    0 2px 2px 0 rgb(0 0 0 / 14%),
    0 3px 1px -2px rgb(0 0 0 / 10%),
    0 1px 5px 0 rgb(0 0 0 / 6%);
}

[data-theme='dark'] .vben-simple-menu-tag {
  color: #fff;
}

[data-theme='dark'] .vben-simple-menu-tag--primary {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-simple-menu-tag--error {
  background-color: #ed6f6f;
}

[data-theme='dark'] .vben-simple-menu-tag--success {
  background-color: #55d187;
}

[data-theme='dark'] .vben-simple-menu-tag--warn {
  background-color: #efbd47;
}

[data-theme='dark'] .vben-editable-cell__icon:hover svg {
  color: #0960bd;
}

[data-theme='dark'] .vben-setting-theme-picker__item {
  border: 1px solid #ddd;
}

[data-theme='dark'] .vben-setting-theme-picker__item--active {
  border: 1px solid #0b79ee;
}

[data-theme='dark'] .vben-setting-theme-picker__item--active svg {
  fill: #fff !important;
}

[data-theme='dark'] .light-border::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title:hover {
  color: #fff;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item-selected,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title-selected {
  background-color: #0960bd !important;
  color: #fff;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item-selected,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title-selected {
  background-color: rgb(9 96 189 / 10%);
  color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item-selected::after,
[data-theme='dark']
  .vben-menu-menu-popover
  .vben-menu-light
  .vben-menu-submenu-title-selected::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-menu-light {
  background-color: #fff;
}

[data-theme='dark'] .vben-menu-light .vben-menu-submenu-active {
  color: #0960bd !important;
}

[data-theme='dark'] .vben-menu-light .vben-menu-submenu-active-border::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-dark .vben-menu-submenu-active {
  color: #fff !important;
}

[data-theme='dark'] .vben-menu-vertical .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-vertical .vben-menu-submenu-title:hover {
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu) {
  background-color: rgb(9 189 33 / 10%);
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu)::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-light.vben-menu-vertical .vben-menu-item-active.vben-menu-submenu {
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active,
[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active {
  background-color: rgb(9 96 189 / 5%);
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active::before,
[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-item,
[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu),
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-submenu-title-active:not(.vben-menu-submenu) {
  background-color: #0960bd !important;
  color: #fff !important;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title:hover {
  color: #fff;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical.vben-menu-collapse .vben-menu-submenu-active,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active {
  color: #fff !important;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active::before,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active::before {
  background-color: #0960bd;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active
  .vben-menu-submenu-collapse,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active
  .vben-menu-submenu-collapse {
  background-color: transparent;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu .vben-menu-item-active,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-submenu
  .vben-menu-item-active:hover {
  color: #fff;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-child-item-active
  > .vben-menu-submenu-title {
  color: #fff;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-opened
  .vben-menu-submenu-has-parent-submenu
  .vben-menu-submenu-title {
  background-color: transparent;
}

[data-theme='dark'] .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark {
  background-color: transparent;
}

[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-active,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-open,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-selected,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-active,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-open,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-selected,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-title:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu:not(.ant-menu-inline)
  .ant-menu-submenu-open {
  color: #fff;
}

[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .vben-basic-menu-item__level1 {
  background-color: transparent;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item {
  background-color: #f0f2f5;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--light::before,
[data-theme='dark'] .vben-setting-menu-type-picker__item--sidebar::before {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--light::after,
[data-theme='dark'] .vben-setting-menu-type-picker__item--sidebar::after {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix::before {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix::after {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--top-menu::after {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--dark {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar::before {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar::after {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar .mix-sidebar {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--active,
[data-theme='dark'] .vben-setting-menu-type-picker__item:hover {
  border: 2px solid #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__check-item .ant-checkbox-wrapper:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left,
[data-theme='dark'] .vben-basic-column-setting__fixed-right {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left.active,
[data-theme='dark'] .vben-basic-column-setting__fixed-left:hover,
[data-theme='dark'] .vben-basic-column-setting__fixed-right.active,
[data-theme='dark'] .vben-basic-column-setting__fixed-right:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left.disabled,
[data-theme='dark'] .vben-basic-column-setting__fixed-right.disabled {
  color: rgb(255 255 255 / 30%);
}
