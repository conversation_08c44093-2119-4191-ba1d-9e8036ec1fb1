/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-18 13:43:24
 * @FilePath     : \tzlink-gps-web\src\api\passport\org.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '@/api/model/baseModel';

/**
 * @description: 机构列表
 */
export function getOrgList(data) {
  return request.post({
    url: `/org/query`,
    data,
  });
}

/**
 * @description: 新建机构
 */
export function addOrg(data) {
  return request.post({
    url: `/org`,
    data,
  });
}

/**
 * @description: 编辑机构
 */
export function editOrg(id, data) {
  return request.put({
    url: `/org/${id}`,
    data,
  });
}

/**
 * @description: 删除机构
 */
export function removeOrg(id) {
  return request.delete({
    url: `/org/${id}`,
  });
}

/**
 * @description: 获取机构树
 */
export function getOrgTreeOptions() {
  return request.get({
    url: `/org/tree`,
  });
}
