import { inject, getCurrentInstance } from 'vue';
import type { DynamicDrawerInstance, DynamicDrawerOptions } from '@/components/Drawer/src/typing';

export const DynamicDrawerSymbol = Symbol();

export interface DrawerServiceContext {
  open: (content: any, options?: DynamicDrawerOptions) => DynamicDrawerInstance;
  closeAll: () => void;
}

export function useDynamicDrawer(): DrawerServiceContext {
  if (!getCurrentInstance()) {
    throw new Error('useDrawer() can only be used inside setup() or functional components!');
  }

  const drawerService = inject<DrawerServiceContext>(DynamicDrawerSymbol);

  if (!drawerService) {
    throw new Error('No dynamic drawer context provided!');
  }

  return drawerService;
}
