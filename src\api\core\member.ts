import { defHttp } from '@/utils/http/axios';

/**
 * @description: 编辑用户信息
 */
export function getMemberList(data) {
  return defHttp.post({
    url: `/member/query`,
    data,
    // permission: 'member-edit'
  });
}

/**
 * @description: 新建用户
 */
export function registerMember(data) {
  return defHttp.post({
    url: `/member/register`,
    data,
    // permission: 'member-add'
  });
}

/**
 * @description: 编辑用户信息
 */
export function editMember(id, data) {
  return defHttp.put({
    url: `/member/${id}`,
    data,
    // permission: 'member-edit'
  });
}

/**
 * @description: 删除用户
 */
export function removeMember(id) {
  return defHttp.delete({
    url: `/member/${id}`,
    // permission: 'member-edit'
  });
}

/**
 * @description: 修改密码
 */
export function changePassword(data) {
  return defHttp.post({
    url: `/member/password/change`,
    data,
    // permission: 'NONE'
  });
}

/**
 * @description: 重置密码
 */
export function resetPassword(data) {
  return defHttp.put({
    url: `/member/password/reset`,
    data,
    // permission: 'member-resetpassword'
  });
}

/**
 * @description: 登录日志列表
 */
export function getSignInLogList(data) {
  return defHttp.get({
    url: `/member/${data.criteria.cuid.value}/signin/${data.pageIndex}`,
    // permission: 'member-resetpassword'
  });
}

/**
 * @description: 获取用户表单数据
 */
export function getMemberDetail(id) {
  return defHttp.get({
    url: `/member/${id}`,
  });
}
