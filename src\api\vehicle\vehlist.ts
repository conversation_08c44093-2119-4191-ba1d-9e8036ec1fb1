import { defHttp as request } from '@/utils/http/axios';

/**
 * @description: 设备列表
 */
export function getvehicleList(data) {
  return request.post({
    url: `/device/query`,
    data,
  });
}

/**
 * @description: 新建设备
 */
export function addDevice(data) {
  return request.post({
    url: `/device`,
    data,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 编辑设备
 */
export function editDevice(id, data) {
  return request.put({
    url: `/device/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}
/**
 * @description: 销售设备
 */
export function saleDevice(data) {
  return request.put({
    url: `/device/sale`,
    data,
  });
}



/**
 * @description:按机构获取车辆树
 */
export function getvehicleTree(data = {}) {
  return request.post({
    url: `/device/tree`,
    data,
  });
}

/**
 * @description: 轨迹回放
 */
export function queryTrack(id, data) {
  return request.post({
    url: `/track/trace/${id}`,
    data,
  });
}

/**
 * @description: 获取设备详情
 */
export function getvehDetail(id) {
  return request.post({
    url: `/device/detail/${id}`,
  });
}



/**
 * @description: 删除设备
 */
export function removevehicle(id) {
  return request.delete({
    url: `/device/${id}`,
    // permission: 'vendor-remove'
  });
}

/**
 * @description: 指令下发
 */
export function sendCommand(code, simNo, data) {
  return request.put(
    {
      url: `/device/send/command/${code}/${simNo}`,
      data,
      // permission: 'vendor-add'
    },
    {
      successMessageMode: 'none',
      errorMessageMode: 'none',
    },
  );
}

/**
 * @description: 报警历史
 */
export function getAlarmHistory(data) {
  return request.post({
    url: `/alert/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 获取指令应答结果
 */
export function getlogresult(id) {
  return request.get(
    {
      url: `/terminal/command-log/${id}`,
    },
    {
      successMessageMode: 'none',
      errorMessageMode: 'none',
    },
  );
}

/**
 * @description: 告警信息
 */
export function getalarmList(data) {
  // return request.post({
  //   url: `/alarmInfo/query`,
  //   data,
  // });
  return new Promise((resolve, reject) => {
    resolve({
      total: 133,
      pageSize: 10,
      totalPage: 14,
      data: [
        {
          id: 11341,
          vehicleId: 68,
          vehicleNo: '96202480043',
          driverName: '',
          content: '1号逻辑通道视频信号丢失报警',
          speed: 0.0,
          location: '',
          beginTime: '2024-03-05 09:07:29',
          endTime: '',
          status: 'ON',
          remark: 'null',
          heading: '紧急报警',
          alarmType: '视频信号丢失报警',
          gcj02Lat: 34.39038,
          gcj02Lng: 117.377727,
          level: 0,
          attachmentList: null,
        },
        {
          id: 11342,
          vehicleId: 68,
          vehicleNo: '96202480043',
          driverName: '',
          content: '2号逻辑通道视频信号丢失报警',
          speed: 0.0,
          location: '',
          beginTime: '2024-03-05 09:07:29',
          endTime: '',
          status: 'ON',
          remark: 'null',
          heading: '紧急报警',
          alarmType: '视频信号丢失报警',
          gcj02Lat: 34.39038,
          gcj02Lng: 117.377727,
          level: 0,
          attachmentList: null,
        },
        {
          id: 11343,
          vehicleId: 9,
          vehicleNo: '96202480039',
          driverName: '',
          content: '2号逻辑通道视频信号丢失报警',
          speed: 0.0,
          location: '',
          beginTime: '2024-03-05 09:08:35',
          endTime: '',
          status: 'ON',
          remark: 'null',
          heading: '紧急报警',
          alarmType: '视频信号丢失报警',
          gcj02Lat: 34.320484,
          gcj02Lng: 117.15662,
          level: 0,
          attachmentList: null,
        },
        {
          id: 11344,
          vehicleId: 7,
          vehicleNo: '96202480038',
          driverName: '',
          content: '2号逻辑通道视频信号丢失报警',
          speed: 14.7,
          location: '',
          beginTime: '2024-03-05 09:09:04',
          endTime: '',
          status: 'ON',
          remark: 'null',
          heading: '紧急报警',
          alarmType: '视频信号丢失报警',
          gcj02Lat: 34.219913,
          gcj02Lng: 117.227134,
          level: 0,
          attachmentList: null,
        },
        {
          id: 11345,
          vehicleId: 62,
          vehicleNo: '96202480041',
          driverName: '',
          content: '驾驶员变更事件',
          speed: 74.5,
          location: '',
          beginTime: '2024-03-05 09:42:46',
          endTime: '',
          status: 'ON',
          remark: '驾驶行为报警',
          heading: '驾驶行为报警',
          alarmType: '驾驶员变更事件',
          gcj02Lat: 34.778169,
          gcj02Lng: 116.951112,
          level: 2,
          attachmentList: null,
        },
        {
          id: 11346,
          vehicleId: 62,
          vehicleNo: '96202480041',
          driverName: '',
          content: '车道偏离报警',
          speed: 71.5,
          location: '',
          beginTime: '2024-03-05 09:49:05',
          endTime: '',
          status: 'ON',
          remark: '主动安全报警',
          heading: '主动安全报警',
          alarmType: '车道偏离报警',
          gcj02Lat: 34.777626,
          gcj02Lng: 116.916572,
          level: 2,
          attachmentList: null,
        },
        {
          id: 11347,
          vehicleId: 62,
          vehicleNo: '96202480041',
          driverName: '',
          content: '车道偏离报警',
          speed: 61.5,
          location: '',
          beginTime: '2024-03-05 10:13:16',
          endTime: '',
          status: 'ON',
          remark: '主动安全报警',
          heading: '主动安全报警',
          alarmType: '车道偏离报警',
          gcj02Lat: 34.80796,
          gcj02Lng: 116.911218,
          level: 2,
          attachmentList: null,
        },
        {
          id: 11348,
          vehicleId: 62,
          vehicleNo: '96202480041',
          driverName: '',
          content: '车道偏离报警',
          speed: 92.7,
          location: '',
          beginTime: '2024-03-05 11:00:48',
          endTime: '',
          status: 'ON',
          remark: '主动安全报警',
          heading: '主动安全报警',
          alarmType: '车道偏离报警',
          gcj02Lat: 34.814579,
          gcj02Lng: 116.924971,
          level: 2,
          attachmentList: null,
        },
        {
          id: 11349,
          vehicleId: 62,
          vehicleNo: '96202480041',
          driverName: '',
          content: '车道偏离报警',
          speed: 104.3,
          location: '',
          beginTime: '2024-03-05 11:04:18',
          endTime: '',
          status: 'ON',
          remark: '主动安全报警',
          heading: '主动安全报警',
          alarmType: '车道偏离报警',
          gcj02Lat: 34.792228,
          gcj02Lng: 116.941521,
          level: 2,
          attachmentList: null,
        },
        {
          id: 11350,
          vehicleId: 67,
          vehicleNo: '96202480042',
          driverName: '',
          content: '驾驶员变更事件',
          speed: 20.0,
          location: '',
          beginTime: '2024-03-05 13:34:51',
          endTime: '',
          status: 'ON',
          remark: '驾驶行为报警',
          heading: '驾驶行为报警',
          alarmType: '驾驶员变更事件',
          gcj02Lat: 34.762908,
          gcj02Lng: 116.92768,
          level: 2,
          attachmentList: null,
        },
      ],
    });
  });
}

/**
 * @description: 行驶里程
 */
export function getWorkdaily(data) {
  // return request.post({
  //   url: `/workDaily/staticData`,
  //   data,
  //   // permission: 'vendor-add'
  // });
  return new Promise((resolve, reject) => resolve([]));
}

/**
 * @description: 获取车辆静态数据
 */
export function getStaticData(data) {
  // return request.post({
  //   url: `/vehicle/staticData`,
  //   data,
  // });
  return new Promise((resolve, reject) => {
    resolve({ offline: 20, access: 20, active: 0, online: 0, totalMileage: 110562.2 });
  });
}

/**
 * @description: 获取车辆列表在线数据
 */
export function getVehData(data) {
  return request.get({
    url: `/device/amount`,
    data,
  });
}

/**
 * @description: 获取车辆里程排行榜
 */
export function getmileageData(data) {
  // return request.post({
  //   url: `/workDaily/mileageData`,
  //   data,
  // });
  return new Promise((resolve, reject) => resolve([]));
}

/**
 * @description: 获取车辆推流列表
 */
export function getpushvehlist() {
  return request.post({
    url: `/device/pushVehicle`,
    // permission: 'vendor-add'
  });
}
