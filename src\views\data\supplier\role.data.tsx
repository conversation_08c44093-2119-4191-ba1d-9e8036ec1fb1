import { BasicColumn, FormSchema } from '@/components/Table';
import { Tag } from 'ant-design-vue';
import { RouterLink } from 'vue-router';

export const columns: BasicColumn[] = [
  {
    title: '供应商名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '视频配置',
    dataIndex: 'settingCount',
    // slot: 'settingCount',
    customRender: ({ record }) => {
      return (
        <RouterLink to={{path:`/data/videosetting/${record.id}`}}>
          <Tag color="blue" class="cursor-pointer" >{
          record.settingCount
        }</Tag>
        </RouterLink>
      )
    }
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '供应商名称',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '供应商名称',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  }
];
