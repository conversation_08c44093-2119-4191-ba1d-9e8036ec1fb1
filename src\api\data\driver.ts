/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-26 14:52:23
 * @FilePath     : \special-front\src\api\data\driver.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';
/**
 * @description: 司机列表
 */
export function getdriverList(data) {
  return request.post({
    url: `/driver/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建司机
 */
export function adddriver(data) {
  return request.post({
    url: `/driver`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑司机
 */
export function editdriver(id, data) {
  return request.put({
    url: `/driver/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}

/**
 * @description: 删除司机
 */
export function removedriver(id) {
  return request.delete({
    url: `/driver/${id}`,
    // permission: 'vendor-remove'
  });
}
