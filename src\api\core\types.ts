export interface IResource {
  children: IResource[];
  code: string;
  httpMethod: IHttpMethod;
  icon: string;
  id: number;
  kind: IResourceType;
  name: string;
  parentId: number;
  path: string;
  sort: number;
  route: string;
}

export enum IResourceType {
  Menu = 'Menu',
  Action = 'Operation',
}

export enum IHttpMethod {
  Get = 'GET',
  Post = 'POST',
  Put = 'PUT',
  Delete = 'DELETE',
  Patch = 'PATCH',
  Options = 'OPTIONS',
  Head = 'HEAD',
  Trace = 'TRACE',
}
