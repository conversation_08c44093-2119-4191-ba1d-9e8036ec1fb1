/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:05
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-20 14:47:56
 * @FilePath     : \special-front\src\views\system\menu\menu.data.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import Icon from '@/components/Icon/Icon.vue';
import { IResourceType, IHttpMethod } from '@/api/passport/types';
import { getAssignableMenuList } from '@/api/passport/resource';

export const columns: BasicColumn[] = [
  {
    title: '资源名称',
    dataIndex: 'name',
    align: 'left',
  },
  {
    title: '资源代码',
    dataIndex: 'code',
  },
  {
    title: '资源类型',
    dataIndex: 'kind',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.kind === IResourceType.Menu ? 'blue' : 'green',
        },
        {
          default: () => (record.kind === IResourceType.Menu ? '菜单' : '功能'),
        },
      );
    },
  },
  {
    title: '资源地址',
    dataIndex: 'route',
  },
  {
    title: '路由地址',
    dataIndex: 'path',
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 100,
    customRender: ({ record }) => {
      return h(Icon, { icon: record.icon });
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100,
  },
];

const isMenu = (kind: string) => kind === IResourceType.Menu;
const isButton = (kind: string) => kind === IResourceType.Action;

export const searchFormSchema: FormSchema[] = [
  {
    field: 'menuName',
    label: '菜单名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'kind',
    label: '资源类型',
    component: 'RadioButtonGroup',
    defaultValue: IResourceType.Menu,
    required: true,
    componentProps: {
      options: [
        { label: '菜单', value: IResourceType.Menu },
        { label: '功能', value: IResourceType.Action },
      ],
    },
    colProps: { lg: 24, md: 24 },
  },
  {
    field: 'name',
    label: '资源名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'parentId',
    label: '上级菜单',
    component: 'ApiTreeSelect',
    defaultValue: null,
    componentProps: {
      api: getAssignableMenuList,
      allowClear: true,
      valueField: 'id',
      labelField: 'name',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'code',
    label: '资源代码',
    component: 'Input',
    required: true,
  },
  {
    field: 'route',
    label: '资源地址',
    component: 'Input',
    defaultValue: null,
    ifShow: ({ values }) => isMenu(values.kind),
  },
  {
    field: 'path',
    label: '路由地址',
    component: 'Input',
    defaultValue: null,
    required: true,
    ifShow: ({ values }) => isMenu(values.kind),
  },
  {
    field: 'iconCss',
    label: '图标',
    component: 'IconPicker',
    defaultValue: null,
    ifShow: ({ values }) => isMenu(values.kind),
  },
  {
    field: 'httpMethod',
    label: 'HTTP方法',
    component: 'Select',
    defaultValue: null,
    componentProps: {
      options: [
        { label: 'GET', value: IHttpMethod.Get },
        { label: 'POST', value: IHttpMethod.Post },
        { label: 'PUT', value: IHttpMethod.Put },
        { label: 'DELETE', value: IHttpMethod.Delete },
        { label: 'PATCH', value: IHttpMethod.Patch },
        { label: 'OPTIONS', value: IHttpMethod.Options },
        { label: 'HEAD', value: IHttpMethod.Head },
        { label: 'TRACE', value: IHttpMethod.Trace },
      ],
    },
    ifShow: ({ values }) => !isButton(values.kind),
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
    },
  },
  // {
  //   field: 'show',
  //   label: '是否显示',
  //   component: 'RadioButtonGroup',
  //   defaultValue: '0',
  //   componentProps: {
  //     options: [
  //       { label: '是', value: '0' },
  //       { label: '否', value: '1' }
  //     ]
  //   },
  //   ifShow: ({ values }) => isMenu(values.kind)
  // }
];
