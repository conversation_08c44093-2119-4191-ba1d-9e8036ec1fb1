<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增设备 </a-button>
      </template>
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
    <DeviceDetail @register="registerDetailModal" @success="handleSuccess" />
    <SaleFormModal @register="registerSaleModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getvehicleList, removevehicle } from '@/api/vehicle/vehlist';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import AddFormModal from './AddFormModal.vue';
  import DeviceDetail from './deviceDetail.vue';
  import SaleFormModal from './saleFormModal.vue';
  import { useDrawer } from '@/components/Drawer';

  import { columns, searchFormSchema } from './vehicle.data';

  export default defineComponent({
    name: 'VehicleList',
    components: { BasicTable, TableAction, Tag, AddFormModal, DeviceDetail, SaleFormModal },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: '设备列表',
        api: getvehicleList,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 150,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: 'right',
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerDrawer] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removevehicle(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }
      //查看设备信息
      const [registerDetailModal, { openModal: openDetailModal }] = useModal();
      const queryDetail = (record) => {
        openDetailModal(true, {
          record,
        });
      };
      //销售设备
      const [registerSaleModal, { openModal: openSaleModal }] = useModal();
      const saleDevice = (record) => {
        openSaleModal(true, {
          record,
        });
      };
      function getDropDownAction(record) {
        return [
          {
            label: '销售',
            onClick: saleDevice.bind(null, record),
          },
          {
            label: '设备详情',
            onClick: queryDetail.bind(null, record),
          },
          {
            label: '查看位置',
          },
          {
            label: '轨迹回放',
          },
          {
            label: '行车记录',
          },
          {
            label: '发送指令',
          },
          {
            label: '查看围栏',
          }
        ];
      }
      function handleSuccess() {
        reload();
      }
      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        registerDrawer,
        getDropDownAction,
        registerDetailModal,
        registerSaleModal,
      };
    },
  });
</script>
