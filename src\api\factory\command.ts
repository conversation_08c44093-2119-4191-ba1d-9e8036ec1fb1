import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 设备列表
 */
export function getCommandList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/command/query`,
    data,
  });
}

/**
 * @description: 新建控制命令
 */
export function addCommand(categoryId, data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/command/${categoryId}`,
    data,
  });
}

/**
 * @description: 编辑控制命令
 */
export function editCommand(categoryId, id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/command/${categoryId}/${id}`,
    data,
  });
}

/**
 * @description: 删除控制命令
 */
export function removeCommand(categoryId, id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/command/${categoryId}/${id}`,
  });
}

/**
 * @description: 控制命令表单数据
 */
export function getCommandDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/command/${id}`,
  });
}

/**
 * @description: 控制命令类型选项
 */
export function getCommandKindOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/command/kind/options`,
  });
}

/**
 * @description: 控制命令预约触发条件选项
 */
export function getCommandPreconditionOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/command/precondition/options`,
  });
}

/**
 * @description: 刷新控制命令结果
 */
export function refreshCommandLog(logId) {
  return defHttp.get(
    {
      url: `${MicroServiceEnum.FACTORY}/command/log/${logId}`,
    },
    {
      errorMessageMode: 'none',
      successMessageMode: 'none',
    },
  );
}

/**
 * @description: 查询控制命令日志
 */
export function getCommandLogList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/command/log/query`,
    data,
  });
}

/**
 * @description: 复制控制命令
 */
export function copyCategoryCommand(categoryId, commandIds) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/command/${categoryId}/copy`,
    data: commandIds,
  });
}
