import { IMG_BASE64 } from './img';

export default {
  legend: [],
  graphic: {
    elements: [
      {
        type: 'image',
        z: 3,
        style: {
          image: IMG_BASE64,
          width: 178,
          height: 178,
        },
        left: 'center',
        top: 'center',
        position: [100, 100],
      },
    ],
  },
  tooltip: {
    show: false,
  },
  toolbox: {
    show: false,
  },
  series: [
    {
      name: '车辆总数',
      type: 'pie',
      clockWise: false,
      radius: [100, 104],
      hoverAnimation: false,
      itemStyle: {
        normal: {
          label: {
            show: true,
            position: 'outside',
            color: '#ddd',
            formatter(params) {
              // console.log('label formatter : ', params);
              if (params.name !== '') {
                return params.name + '\n' + '\n' + ' ' + Math.ceil(params.value) + '%';
              } else {
                return '';
              }
            },
          },
          labelLine: {
            length: 20,
            length2: 50,
            show: true,
            color: '#00ffff',
          },
        },
      },
      data: [],
    },
  ],
};
