import {
  presetTypography,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss';
import UnoCSS from 'unocss/vite';
import { type UserConfig } from 'vite';

const commonConfig: (mode: string) => UserConfig = (mode) => ({
  server: {
    host: true,
    open: true,
  },
  esbuild: {
    //drop: mode === 'production' ? ['console', 'debugger'] : [],
    drop: [],
  },
  build: {
    reportCompressedSize: false,
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      // TODO: Prevent memory overflow
      maxParallelFileOps: 3,
    },
  },
  plugins: [
    UnoCSS({
      presets: [presetUno(), presetTypography()],
      transformers: [transformerDirectives(), transformerVariantGroup()],
      safelist: 'prose m-auto text-left'.split(' '),
    }),
  ],
});

export { commonConfig };
