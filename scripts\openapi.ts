const { generateService } = require('@umijs/openapi');

generateService({
  schemaPath: 'http://*************:3330/v3/api-docs',
  serversPath: './generate',
  enumStyle: 'enum',
  apiPrefix: 'MicroServiceEnum.FACTORY',
  requestImportStatement: `import { defHttp } from '@/utils/http/axios';\nimport { MicroServiceEnum } from '@/api/model/baseModel';\nconst request = defHttp.request`,
  projectName: 'mantall',
});
