<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form/index';

  import { FormSchema } from '@/components/Table';
  import { useDynamicModalContext } from '@/components/Modal/src/hooks/useModalContext';
  import { HttpStatusEnum } from '@/enums/httpEnum';
  import { MemeberTypeEnum } from '@/enums/roleEnum';
  import { addvideoStreamSetting, editvideoStreamSetting } from '@/api/data/supplier';

  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '推流地址',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
    },
    {
      field: 'baseUrl',
      label: '基础路径',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
    },
    {
      field: 'secret',
      label: 'secret',
      required: false,
      colProps: { span: 24 },
      component: 'Input',
    },
    {
      field: 'secretKey',
      label: 'secretKey',
      required: false,
      colProps: { span: 24 },
      component: 'Input',
    },
    {
      field: 'type',
      label: '类型',
      component: 'Select',
      defaultValue: null,
      colProps: { span: 24 },
      componentProps: {
        options: [
          { label: '推流', value: 'Stream' },
          { label: '内嵌frame', value: 'Frame' },
        ],
      },
    },
  ];

  const [registerForm, { setFieldsValue, validate, validateFetchError }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const { closeModal, instance, changeConfirmLoading } = useDynamicModalContext();

  onMounted(() => {
    if (instance.data.modalType === 'edit') {
      // getEquipDetail(instance.data.id).then((res) => {
      //   if (res) {
      //     setFieldsValue(res);
      //   }
      // });
      setFieldsValue(instance.data);
    }
  });

  async function onDone() {
    changeConfirmLoading(true);
    try {
      const values = await validate();
      values.vendorId = instance.data.vendorId;

      if (instance.data.modalType === 'edit') {
        await editvideoStreamSetting(instance.data.id, values);
      } else {
        await addvideoStreamSetting(values);
      }
      closeModal(values);
    } catch (e: any) {
      if (e.status === HttpStatusEnum.BAD_REQUEST) {
        validateFetchError(e.data);
      }
    } finally {
      changeConfirmLoading(false);
    }
  }

  defineExpose({
    onDone,
  });
</script>

<template>
  <BasicForm @register="registerForm" />
</template>
