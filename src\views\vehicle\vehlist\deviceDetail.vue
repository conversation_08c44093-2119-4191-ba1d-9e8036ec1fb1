<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-20 17:07:25
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-24 17:36:18
 * @FilePath     : \tzlink-gps-web\src\views\vehicle\vehlist\deviceDetail.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { addDevice, editDevice } from '@/api/vehicle/vehlist';
  import { basicFormSchema, simFormSchema, customerFormSchema, installFormSchema } from './device.data';

  const activeKey = ref('basic');
  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);
  //基础信息
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: basicFormSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });
  //SIM卡信息
  const [registerSimForm, { setFieldsValue: setSIMFieldsValue, resetFields: resetSIMFields }] =
    useForm({
      labelWidth: 100,
      schemas: simFormSchema,
      showActionButtonGroup: false,
      actionColOptions: {
        span: 23,
      },
    });

  //客户信息
  const [
    registerCustomerForm,
    {
      setFieldsValue: setCustomerFieldsValue,
      resetFields: resetCustomerFields,
      validate: customerValidate,
    },
  ] = useForm({
    labelWidth: 100,
    schemas: customerFormSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });
  //安装信息
  const [
    registerInstallForm,
    {
      setFieldsValue: setInstallFieldsValue,
      resetFields: resetInstallFields,
      validate: installValidate,
    },
  ] = useForm({
    labelWidth: 100,
    schemas: installFormSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });
  //赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    resetSIMFields();
    resetCustomerFields();
    resetInstallFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    rowId.value = data.record.id;
    setFieldsValue({
      ...data.record,
    });
    setSIMFieldsValue({
      ...data.record.sim,
    });
    setCustomerFieldsValue({
      ...data.record.deviceCustomer,
    });
    setInstallFieldsValue({
      ...data.record.deviceInstall,
    });
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      const customerValue = await customerValidate();
      const installValue = await installValidate();
      setModalProps({ confirmLoading: true });
      let params = {
        ...values,
        customerForm: customerValue,
        installForm: installValue,
      };
      // TODO custom api
      await editDevice(rowId.value, params);
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal
    width="1000px"
    v-bind="$attrs"
    title="设备详情"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <Tabs v-model:activeKey="activeKey">
      <TabPane key="basic" tab="设备信息">
        <div class="p-2" style="max-height: 400px; overflow: auto">
          <h3 class="p-2">基础信息</h3>
          <hr />
          <BasicForm class="mt-6" @register="registerForm" />

          <h3 class="p-2">SIM卡信息</h3>
          <hr />
          <BasicForm class="mt-6" @register="registerSimForm" />
        </div>
      </TabPane>
      <a-tab-pane key="customer" tab="客户信息">
        <div class="p-2" style="max-height: 400px; overflow: auto">
          <h3 class="p-2">客户信息</h3>
          <hr />
          <BasicForm class="mt-6" @register="registerCustomerForm" />

          <h3 class="p-2">安装信息</h3>
          <hr />
          <BasicForm class="mt-6" @register="registerInstallForm" />
        </div>
      </a-tab-pane>
    </Tabs>
  </BasicModal>
</template>
