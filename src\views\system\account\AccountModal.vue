<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form/index'
import { accountFormSchema } from './account.data'
import { registerMember, editMember } from '@/api/passport/member'

export default defineComponent({
  name: 'AccountModal',
  components: { BasicModal, BasicForm },
  emits: ['success', 'register'],
  setup(_, { emit }) {
    const isUpdate = ref(true)
    const uid = ref<number | undefined>(undefined)

    const [
      registerForm,
      { setFieldsValue, updateSchema, resetFields, validate, validateFields }
    ] = useForm({
      labelWidth: 100,
      schemas: accountFormSchema,
      showActionButtonGroup: false,
      actionColOptions: {
        span: 23
      }
    })

    const [registerModal, { setModalProps, closeModal }] = useModalInner(
      async data => {
        resetFields()
        setModalProps({ confirmLoading: false })
        isUpdate.value = !!data?.isUpdate

        if (unref(isUpdate)) {
          uid.value = data.record.uid
          setFieldsValue({
            ...data.record
          })
        }

        updateSchema([
          {
            field: 'password',
            show: !unref(isUpdate),
            required: false,
          },
          {
            field: 'cpwd',
            show: !unref(isUpdate),
            required: false,
          }
        ])
      }
    )

    const getTitle = computed(() =>
      !unref(isUpdate) ? '新增账号' : '编辑账号'
    )

    async function handleSubmit() {
      console.log('handleSubmit')
      try {
        // TODO custom api
        let values
        if (unref(isUpdate)) {
          values = await validate([
            'loginName',
            'email',
            'expire',
            'memberType',
            'mobile',
            'name',
            'roles'
          ])
          setModalProps({ confirmLoading: true })
          console.log(values, unref(isUpdate), JSON.stringify(values))
          await editMember(uid.value, values)
        } else {
          values = await validate()
          setModalProps({ confirmLoading: true })
          console.log(values, unref(isUpdate), JSON.stringify(values))
          await registerMember(values)
        }
        closeModal()
        emit('success', {
          isUpdate: unref(isUpdate),
          values: { ...values, uid: uid.value }
        })
      } catch (e: any) {
        console.log('error', e, e.response)
      } finally {
        setModalProps({ confirmLoading: false })
      }
    }

    return { registerModal, registerForm, getTitle, handleSubmit }
  }
})
</script>
