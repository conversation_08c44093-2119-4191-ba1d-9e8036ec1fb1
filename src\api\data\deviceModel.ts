import { defHttp } from '@/utils/http/axios';

/**
 * @description: 新建设备型号
 */
export function addModel(data) {
  return defHttp.post({
    url: `/deviceModel`,
    data,
  });
}

/**
 * @description: 编辑设备型号
 */
export function editModel(id, data) {
  return defHttp.put({
    url: `/deviceModel/${id}`,
    data,
  });
}
/**
 * @description: 删除设备型号
 */
export function removeModel(id) {
  return defHttp.delete({
    url: `/deviceModel/${id}`,
  });
}

/**
 * @description: 设备型号列表
 */
export function getModelList(data) {
  return defHttp.post({
    url: `/deviceModel/query`,
    data,
  });
}

/**
 * @description: 型号表单数据
 */
export function getModelDetail(id) {
  return defHttp.get({
    url: `/deviceModel/${id}`,
  });
}

/**
 * @description: 型号树
 */
export function getModelTree() {
  return defHttp.get({
    url: `/deviceModel/tree`,
  });
}