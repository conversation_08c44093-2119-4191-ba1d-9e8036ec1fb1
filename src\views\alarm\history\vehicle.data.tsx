/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-10-06 08:53:36
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-06 11:56:41
 * @FilePath     : \special-front\src\views\alarm\history\vehicle.data.tsx
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getOrgTreeOptions } from '@/api/passport/org';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '车架号',
    dataIndex: 'vin',
    width: 280,
  },
  {
    title: '所属机构',
    dataIndex: 'org',
  },
  {
    title: '报警类型',
    dataIndex: 'type',
  },
  {
    title: '报警位置',
    dataIndex: 'gpsPosition',
  },
  {
    title: '开始时间',
    dataIndex: 'beginTime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
  },
  {
    title: '报警摘要',
    dataIndex: 'content',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'criteria.vin',
    label: '车架号',
    labelWidth: 80,
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'criteria.orgUid',
    label: '所属机构',
    component: 'ApiTreeSelect',
    labelWidth: 80,
    colProps: { span: 8 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'criteria.type',
    label: '报警类型',
    component: 'Select',
    defaultValue: null,
    labelWidth: 80,
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '超速报警', value: 'SPEED' },
        { label: '疲劳驾驶', value: 'TIRED' },
        { label: '安全带报警', value: 'BELT' },
        { label: '其他报警', value: 'OTHER' },
      ],
    },
  },
  {
    field: 'criteria.range',
    label: '日期范围',
    component: 'RangePicker',
    labelWidth: 80,
    colProps: { span: 12 },
    defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    componentProps: {
      style: 'width: 100%;',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
