<script lang="ts" setup>
  import { watch } from 'vue';
  import { useECharts } from './hooks/useECharts';
  import type { EChartsOption } from 'echarts';

  const props = defineProps<{
    options: EChartsOption;
    theme?: 'light' | 'dark' | 'default';
  }>();
  const { elRef, setOptions, getInstance } = useECharts(props.theme);
  watch(
    () => props.options,
    (options) => {
      setOptions(options);
    },
    { deep: true, immediate: true },
  );

  defineExpose({
    getInstance,
  });
</script>

<template>
  <div class="w-full h-full" ref="elRef"></div>
</template>
