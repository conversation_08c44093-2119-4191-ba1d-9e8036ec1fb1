<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:05
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-19 17:19:10
 * @FilePath     : \tzlink-gps-web\src\views\data\deviceModel\addFormModal.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './deviceModel';
  import { addModel, editModel } from '@/api/data/deviceModel';

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
        orgId: data.record.orgUid,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增设备类型' : '编辑设备类型'));

  async function handleSubmit() {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      // TODO custom api
      if (unref(isUpdate)) {
        await editModel(rowId.value, values);
      } else {
        await addModel(values);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
./simCard.data./deviceModel