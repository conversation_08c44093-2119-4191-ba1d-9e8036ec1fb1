import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 新建围栏
 */
export function addFence(data) {
  return request.put({
    url: `/fence`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑围栏
 */
export function editFence(id, data) {
  return request.put({
    url: `/fence/${id}`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 查询围栏
 */
export function getFenceList(data) {
  return request.post({
    url: `/fence/query`,
    data,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 删除围栏
 */
export function removeFence(id) {
  return request.delete({
    url: `/fence/${id}`,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 禁用围栏
 */
export function unUseFence(ids) {
  return request.post({
    url: `/fence/stop/${ids}`,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 启用围栏
 */
export function useFence(ids) {
  return request.post({
    url: `/fence/open/${ids}`,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 当前围栏报警
 */
export function getFenceAlarmList(data) {
  return request.post({
    url: `/device-fence-alert/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 查询围栏车辆关联
 */
export function getFenceCarList(data) {
  return request.post({
    url: `/device-fence/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 删除关联车辆
 */
export function removeFenceCar(id) {
  return request.delete({
    url: `/device-fence/deleteRel/${id}`,
    // permission: 'vendor-add'
  });
}
