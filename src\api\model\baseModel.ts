/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-28 11:35:56
 * @FilePath     : \special-front\src\api\model\baseModel.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T> {
  items: T[];
  total: number;
}

export enum MicroServiceEnum {
  FACTORY = '/factory',
  PASSPORT = '/passport',
  MONITOR = '/monitor',
  FORK = '/special-backend/fork-backend',
  GW = '/special-gw/api',
}
