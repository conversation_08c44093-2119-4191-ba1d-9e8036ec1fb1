import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '通道编号',
    dataIndex: 'channelNo',
    width: 280,
  },
  {
    title: '通道名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '在线状态',
    dataIndex: 'online',
    width: 280,
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.type === 'Off' ? 'blue' : 'green',
        },
        {
          default: () => (record.type === 'Off' ? '离线' : '在线'),
        },
      );
    },
  },
  {
    title: '云台类型',
    dataIndex: 'ptHead',
    width: 280,
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.type === 'Normal' ? 'blue' : 'green',
        },
        {
          default: () => (record.type === 'Normal' ? '普通' : '球型'),
        },
      );
    },
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 280,
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.type === 0 ? 'blue' : 'green',
        },
        {
          default: () => (record.type === 0 ? '默认' : '默认'),
        },
      );
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'channelNo',
    label: '通道编号',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'channelNo',
    label: '通道编号',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
  {
    field: 'name',
    label: '通道名称',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 24 },
    componentProps: {
      options: [{ label: '默认', value: 0 }],
    },
  },
  {
    field: 'ptHead',
    label: '云台类型',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '普通', value: 'Normal' },
        { label: '球形', value: 'Spherical' },
      ],
    },
  },
  {
    field: 'online',
    label: '在线状态',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '在线', value: 'On' },
        { label: '离线', value: 'Off' },
      ],
    },
  },
];
