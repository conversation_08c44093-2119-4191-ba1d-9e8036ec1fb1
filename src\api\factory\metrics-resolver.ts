import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 工况列表
 */
export function getMetricsResolverListData(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver/query`,
    data,
  });
}

/**
 * @description: 新建工况
 */
export function addMetricsResolver(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver`,
    data,
  });
}

/**
 * @description: 编辑工况
 */
export function editMetricsResolver(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver/${id}`,
    data,
  });
}

/**
 * @description: 删除工况
 */
export function removeMetricsResolver(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver/${id}`,
  });
}

/**
 * @description: 型号表单数据
 */
export function getMetricsResolverDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver/${id}`,
  });
}

/**
 * @description: 型号表单数据
 */
export function getMetricsResolverOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metrics-resolver/options`,
  });
}
