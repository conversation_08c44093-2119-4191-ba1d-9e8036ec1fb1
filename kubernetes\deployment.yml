apiVersion: v1
kind: Service
metadata:
  namespace: special
  name: special-fronted
  labels:
    application/name: special-fronted
spec:
  selector:
    application/name: special-fronted
  type: NodePort
  ports:
    - name: port1
      protocol: TCP
      port: 80
      targetPort: 80

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: special
  name: special-fronted
  labels:
    application/name: special-fronted
  annotations:
    app.devops/email: <EMAIL>
spec:
  replicas: 1
  selector:
    matchLabels:
      application/name: special-fronted
  template:
    metadata:
      labels:
        application/name: special-fronted
    spec:
      imagePullSecrets:
        - name: harbor
      containers:
        - name: ui
          imagePullPolicy: Always
          image: hub.tizacloud.cn:5000/tsg/special-front:NUMBER
          ports:
            - containerPort: 80
              protocol: TCP
          env:
            - name: GATEWAY_URL
              value: special-gateway
