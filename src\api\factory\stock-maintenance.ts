import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 库存保养设置列表
 */
export function getStockMaintenanceList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/stock-maintenance/query`,
    data,
  });
}

/**
 * @description: 新建库存保养设置
 */
export function addStockMaintenance(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/stock-maintenance`,
    data,
  });
}

/**
 * @description: 编辑库存保养设置
 */
export function editStockMaintenance(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/stock-maintenance/${id}`,
    data,
  });
}

/**
 * @description: 删除库存保养设置
 */
export function removeStockMaintenance(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/stock-maintenance/${id}`,
  });
}

/**
 * @description: 存保养设置表单数据
 */
export function getStockMaintenanceDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/stock-maintenance/${id}`,
  });
}
