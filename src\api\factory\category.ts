import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 设备型号列表
 */
export function getCategoryList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/category/query`,
    data,
  });
}

/**
 * @description: 新建设备型号
 */
export function addCategory(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/category`,
    data,
  });
}

/**
 * @description: 编辑设备型号
 */
export function editCategory(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/category/${id}`,
    data,
  });
}

/**
 * @description: 删除设备型号
 */
export function removeCategory(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/category/${id}`,
  });
}

/**
 * @description: 型号表单数据
 */
export function getCategoryDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/category/${id}`,
  });
}

/**
 * @description: 型号树
 */
export function getCategoryTree() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/category/tree`,
  });
}

/**
 * @description: 获取本地路由选项
 */
export function getResolverRouteOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/category/resolver/route/options`,
  });
}

/**
 * @description: 按型号获取解析配置
 */
export function getResolversByCategoryId(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/category/${id}/resolvers`,
  });
}

/**
 * @description: 添加设备型号配置解析
 */
export function addCategoryResolver(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/category/resolver`,
    data,
  });
}

/**
 * @description: 删除设备型号配置解析
 */
export function removeCategoryResolver(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/category/resolver/${id}`,
  });
}
