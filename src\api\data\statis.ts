/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-28 13:24:09
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-09 17:11:23
 * @FilePath     : \special-front\src\api\data\statis.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 按日统计工时
 */
export function getDailyWorktime(data) {
  return defHttp.post({
    url: `/daily/work/query`,
    data,
  });
}

/**
 * @description: 获取工作时长报表
 */
export function getTotalstatistic(data) {
  return defHttp.post({
    url: `/daily/equips/query`,
    data,
  });
}
/**
 * @description: 获取司机聚合统计作业信息
 */
export function getDriverStatistic(data) {
  return defHttp.post({
    url: `/special-backend/forkWorkCycle/driver/query`,
    data,
  });
}
/**
 * @description: 获取司机工作统计
 */
export function getDriverDaily(data) {
  return defHttp.post({
    url: `/special-backend/forkWorkCycle/daily`,
    data,
  });
}
