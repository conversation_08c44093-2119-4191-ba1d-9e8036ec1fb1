<template>
  <div class="home_div">
    <div class="list">
      <div v-for="veh in vehList" class="card" :class="code == veh.code ? 'active' : ''" @click="selectVeh(veh)">
        {{ veh.name }}
      </div>

    </div>
    <div style="width: 100%; height: 100%;padding: 20px">
      <iframe :src="src" frameborder="0" style="width: 100%; height: 100%"></iframe>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getYSYToken } from '@/api/lift/lift'
const baseUrl = 'https://open.ys7.com/ezopen/h5/iframe?url=ezopen://open.ys7.com'

let src = ref('')
let code = ref('FH0281881')
let channel = ref('1')
let accessToken = ref('at.b7xtiko3avn6hd2b5om7ag1p6ps5j0cj-175do4ebxn-02izr1p-frssvnlcw')
const vehList = [{
  name: '金鑫泰-生产车间',
  code: 'FH0281881',
  channelNo: '1',
}, {
  name: '视频1@DS-7804N-K1/C(D)(FF8381882)',
  code: 'FF8381882',
  channelNo: '1',
}]

onMounted(() => {
  getToken()


});

function getToken() {
  getYSYToken().then(res => {
    console.log(res)
    accessToken.value = res.accessToken
    src.value = `${baseUrl}/${code.value}/${channel.value}.hd.live&autoplay=1&accessToken=${accessToken.value}`;
  })

}

function selectVeh(veh: any) {
  code.value = veh.code
  src.value = `${baseUrl}/${veh.code}/${veh.channelNo}.live&autoplay=1&accessToken=${accessToken.value}`;

}
</script>

<style scoped lang="less">
.home_div {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: none;
}

.list {
  width: 400px;
  margin: 20px;

  .card {
    height: 50px;
    margin-bottom: 4px;
    padding-left: 15px;
    border-radius: 5px;
    background-color: rgb(255 255 255 / 10%);
    box-shadow: 0 0 2px #001e27;
    color: #fff;
    line-height: 50px;
    cursor: pointer;


  }

  .active {
    background-color: #0795c0;
    font-weight: bold;
  }
}
</style>
