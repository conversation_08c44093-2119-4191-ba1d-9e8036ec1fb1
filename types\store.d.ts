import { ErrorTypeEnum } from '@/enums/exceptionEnum';
import { MenuModeEnum, MenuTypeEnum } from '@/enums/menuEnum';
import { RoleInfo } from '@/api/sys/model/userModel';

// Lock screen information
export interface LockInfo {
  // Password required
  pwd?: string | undefined;
  // Is it locked?
  isLock?: boolean;
}

// Error-log information
export interface ErrorLogInfo {
  // Type of error
  type: ErrorTypeEnum;
  // Error file
  file: string;
  // Error name
  name?: string;
  // Error message
  message: string;
  // Error stack
  stack?: string;
  // Error detail
  detail: string;
  // Error url
  url: string;
  // Error time
  time?: string;
}

export type RoleInfo = string;

export interface IUserInfo {
  authorizations: RoleInfo[];
  name: string;
  token: string;
  uid: string;
  application?: string;
  memberType?: IMemberType;
  profile?: IUserInfoProfile;
  ttl?: number;
  homePath?: string;
}

export interface IUserInfoProfile {
  title: string;
  logo: string;
  theme: string;
}

export type IMemberType = 'ADMIN' | 'User';

export interface BeforeMiniState {
  menuCollapsed?: boolean;
  menuSplit?: boolean;
  menuMode?: MenuModeEnum;
  menuType?: MenuTypeEnum;
}
