<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-10 15:39:35
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-16 16:11:40
 * @FilePath     : \tzlink-gps-web\src\App.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<template>
  <ConfigProvider :locale="getAntdLocale" :theme="isDark ? darkTheme : commonTheme">
    <AppProvider>
      <RouterView />
      <DynamicModal />
      <DynamicDrawer />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
  import { ConfigProvider } from 'ant-design-vue';
  import { AppProvider } from '@/components/Application';
  import { useTitle } from '@/hooks/web/useTitle';
  import { useLocale } from '@/locales/useLocale';

  import 'dayjs/locale/zh-cn';
  import { useDarkModeTheme } from '@/hooks/setting/useDarkModeTheme';

  import DynamicModal from '@/components/Modal/src/DynamicModal.vue';
  import DynamicDrawer from '@/components/Drawer/src/DynamicDrawer.vue';
  import { useAppStoreWithOut } from '@/store/modules/app';


  // support Multi-language
  const { getAntdLocale } = useLocale();

  const { isDark, darkTheme } = useDarkModeTheme();

  // Listening to page changes and dynamically changing site titles
  useTitle();
  const appStore = useAppStoreWithOut();
  appStore.checkIsChina();

  const commonTheme = {
    token: {
      colorPrimary: '#0421bc',
    },
  };
</script>
