<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './role.data';
  import { addvideoStreamSetting, editvideoStreamSetting } from '@/api/data/supplier';

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);
  const vendorId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data);
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    vendorId.value = data.record.vendorId;
    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增配置' : '编辑配置'));

  async function handleSubmit() {
    try {
      const values = await validate();
      values.vendorId = vendorId.value;
      setModalProps({ confirmLoading: true });
      // TODO custom api
      console.log(values);
      if (unref(isUpdate)) {
        await editvideoStreamSetting(rowId.value, values);
      } else {
        await addvideoStreamSetting(values);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
