import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';
/**
 * @description: 通道列表
 */
export function getchannelList(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/channel/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建通道
 */
export function addchannel(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/channel`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑通道
 */
export function editchannel(id, data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/channel/edit/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}


/**
 * @description: 删除通道
 */
export function removechannel(id) {
  return request.delete({
    url: `${MicroServiceEnum.MONITOR}/channel/${id}`,
    // permission: 'vendor-remove'
  });
}

/**
 * @description: 获取通道选项
 */
export function getchannelOptions() {
  return request.get({
    url: `${MicroServiceEnum.MONITOR}/channel/option`,
  });
}


