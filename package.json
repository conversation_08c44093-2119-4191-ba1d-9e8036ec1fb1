{"name": "tiza-admin", "version": "2.10.1", "license": "MIT", "scripts": {"bootstrap": "npm install", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:analyze": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode analyze", "build:docker": "vite build --mode docker", "build:no-cache": "npm run clean:cache && npm run build", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "commit": "czg", "dev": "vite", "lint": "turbo run lint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write .", "lint:stylelint": "stylelint \"**/*.{vue,css,less}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "openapi": "ts-node ./scripts/openapi.ts", "preview": "npm run build && vite preview", "reinstall": "rimraf package-lock.json && rimraf node_modules && npm run bootstrap", "serve": "npm run dev", "test:gzip": "npx http-server dist --cors --gzip -c-1", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^6.1.0", "@codemirror/lang-html": "^6.4.6", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/legacy-modes": "^6.3.3", "@codemirror/theme-one-dark": "^6.1.2", "@easydarwin/easyplayer": "^5.1.1", "@iconify/iconify": "^3.1.1", "@vue/shared": "^3.3.4", "@vueuse/core": "^10.2.1", "@vueuse/shared": "^10.2.1", "@zxcvbn-ts/core": "^3.0.2", "ant-design-vue": "^4.0.3", "axios": "^1.4.0", "codemirror": "^6.0.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "echarts": "^5.4.2", "flv.js": "^1.6.2", "lodash-es": "^4.17.21", "mqtt": "^4.3.7", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "^2.1.4", "qrcode": "^1.5.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "unocss": "^0.56.5", "vite-auto-i18n-plugin": "^1.1.5", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.4", "vue-router": "^4.2.3", "vue-types": "^5.1.0", "vue-video-player": "^5.0.2", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/json": "^2.2.87", "@purge-icons/generated": "^0.9.0", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^11.0.1", "@types/lodash-es": "^4.17.7", "@types/node": "^20.8.6", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.1", "@types/qs": "^6.9.7", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "@umijs/openapi": "^1.9.2", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "^3.3.4", "@vue/test-utils": "^2.4.0", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "eslint": "^8.46.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.17.0", "fs-extra": "^11.1.1", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "13.2.3", "pkg-types": "^1.0.3", "postcss": "^8.4.24", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-scss": "^4.0.6", "prettier": "^3.0.0", "prettier-plugin-packagejson": "^2.4.4", "rimraf": "^5.0.1", "rollup-plugin-visualizer": "^5.9.2", "stylelint": "^15.10.1", "stylelint-config-property-sort-order-smacss": "^9.1.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^10.0.0", "stylelint-order": "^6.0.3", "stylelint-prettier": "^3.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "unbuild": "^1.2.1", "unplugin-config": "^0.1.3", "vite": "^4.4.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^3.1.0", "vite-plugin-html": "^3.2.0", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.4"}, "engines": {"node": ">=16.15.1"}}