<template>
  <div :id="mapid" class="map"></div>
</template>

<script>
  import { ref, onMounted, watch, defineComponent } from 'vue';

  export default defineComponent({
    props: {
      info: {
        type: Object,
        default: null,
      },
    },
    setup(props) {
      const mapid = ref('item');
      const map = ref(null);

      // 在 setup 中创建组件时初始化 mapid
      mapid.value = 'map' + props.info.id;

      const initMap = () => {
        if (!map.value) {
          map.value = new AMap.Map(mapid.value, {
            zoom: 7,
            scrollWheel: true,
          }); // 创建地图实例
        }

        const data = props.info;
        if (data && data.districts.length > 0) {
          map.value.setCity(data.districts[0].name); // 设置地图显示的城市
        }

        map.value.clearMap(); // 清除地图覆盖物

        if (data) {
          let json = JSON.parse(data.fenceJson || '[]');
          switch (data.fenceType) {
            case 'CIRCULAR':
              AMap.plugin('AMap.Circle', function () {
                const circle = new AMap.Circle({
                  map: map.value,
                  center: json[0],
                  radius: json[1],
                  fillColor: '#00b0ff',
                  fillOpacity: 0.3,
                  strokeWeight: 1,
                  strokeColor: '#80d8ff',
                });
                map.value.add(circle);
                map.value.setFitView([circle]);
              });
              break;
            case 'POLYGON':
              AMap.plugin('AMap.Polygon', function () {
                const polygon = new AMap.Polygon({
                  map: map.value,
                  path: json,
                  fillColor: '#00b0ff',
                  strokeColor: '#80d8ff',
                  strokeOpacity: 1,
                  fillOpacity: 0.5,
                  strokeWeight: 1,
                  strokeDasharray: [5, 5],
                });
                map.value.add(polygon);
                map.value.setFitView([polygon]);
              });
              break;
            default:
              data.districts.forEach((item) => {
                AMap.plugin('AMap.DistrictSearch', function () {
                  let districtSearch = new AMap.DistrictSearch({
                    level: 'district',
                    subdistrict: 0,
                    extensions: 'all',
                  });

                  districtSearch.search(item.name, function (status, result) {
                    if (status === 'complete' && result.info === 'OK') {
                      let bounds = result.districtList[0].boundaries;
                      if (bounds) {
                        let polygons = [];
                        for (let i = 0; i < bounds.length; i++) {
                          polygons.push(
                            new AMap.Polygon({
                              map: map.value,
                              path: bounds[i],
                              strokeWeight: 2,
                              strokeOpacity: 0.5,
                              strokeColor: '#527fff',
                              fillColor: '#2b866d',
                              fillOpacity: 0.35,
                            }),
                          );
                        }

                        // 调整视口以适应所有多边形
                        map.value.setFitView(polygons);
                      }
                    }
                  });
                });
              });
              break;
          }
        }
      };

      onMounted(() => {
        initMap();
      });

      watch(
        () => props.info,
        (newVal) => {
          if (newVal) {
            initMap();
          }
        },
      );

      return {
        mapid,
        map,
        initMap,
      };
    },
  });
</script>

<style>
  .map {
    width: 296px;
    height: 150px;
  }
</style>
