import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 新建工况分组
 */
export function addMetric(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/metric-group`,
    data,
  });
}

/**
 * @description: 获取工况分组下拉框选项
 */
export function getMetricGroupOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metric-group/options`,
  });
}
