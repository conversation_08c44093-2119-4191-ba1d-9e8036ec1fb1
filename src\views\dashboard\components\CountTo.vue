<script lang="ts" setup>
  const props = defineProps({
    count: {
      type: Number,
      default: 0,
    },
  });
</script>
<template>
  <span class="count-to">{{ props.count }}</span>
</template>
<style lang="less" scoped>
  .count-to {
    display: inline-block;
    padding: 11px 10px;
    border-radius: 3px;
    background: linear-gradient(
      to bottom,
      rgb(184 0 23 / 50%),
      rgb(184 0 23 / 100%),
      rgb(184 0 23 / 50%)
    );
    font-size: 24px;
    line-height: 24px;
  }
</style>
