import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 角色列表
 */
export function getRoleList(data) {
  return defHttp.post({
    url: `/role/query`,
    data,
    // permission: 'role-add'
  });
}

/**
 * @description: 新建角色
 */
export function addRole(data) {
  return defHttp.post({
    url: `/role`,
    data,
    // permission: 'role-add'
  });
}

/**
 * @description: 编辑角色
 */
export function editRole(id, data) {
  return defHttp.put({
    url: `/role/${id}`,
    data,
    // permission: 'role-edit'
  });
}

/**
 * @description: 删除角色
 */
export function removeRole(id) {
  return defHttp.delete({
    url: `/role/${id}`,
    // permission: 'role-remove'
  });
}

/**
 * @description: 获取角色可访问的资源
 */
export function getRoleAuthorize(id) {
  return defHttp.get({
    url: `/role/${id}/authorize`,
    method: 'get',
    // permission: 'NONE'
  });
}

/**
 * @description: 添加角色可访问的资源
 */
export function setRoleAuthorize(data) {
  return defHttp.put({
    url: `/role/authorize`,
    data,
    // permission: 'role-authorize'
  });
}

/**
 * @description: 删除角色可访问的资源
 */
export function removeRoleAuthorize(data) {
  return defHttp.delete({
    url: `/role/authorize`,
    data,
    // permission: 'role-authorize'
  });
}

/**
 * @description: 获取角色下拉框
 */
export function getRoleOptions() {
  return defHttp.get({
    url: `/role/options`,
    // permission: 'NONE'
  });
}

export function getMemberType() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/membership/member-type/options`,
  });
}
