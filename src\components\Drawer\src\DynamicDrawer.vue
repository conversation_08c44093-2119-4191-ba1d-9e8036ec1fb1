<script setup lang="ts">
  import type {
    DynamicDrawerEmitterOpenOptions,
    DynamicDrawerEmitterCloseOptions,
    DynamicDrawerInstance,
    DynamicContentExposeMethods,
  } from './typing';

  import { ref, unref } from 'vue';
  import { BasicDrawer } from '@/components/Drawer';
  import DynamicDrawerEmitter from './emitter';

  defineOptions({
    name: 'DynamicDrawer',
    inheritAttrs: false,
  });

  const instanceMap = ref<Recordable<DynamicDrawerInstance>>({});

  const contentCompRefs = ref<DynamicContentExposeMethods[]>([]);

  function openListener({ instance }: DynamicDrawerEmitterOpenOptions): void {
    instance.open = true;
    unref(instanceMap)[instance.key] = instance;
  }

  function closeListener({ instance, params }: DynamicDrawerEmitterCloseOptions): void {
    const _instance: DynamicDrawerInstance = unref(instanceMap)[instance.key!];

    if (_instance) {
      _instance.open = false;
      // 回调onDone
      _instance.options.onDone && _instance.options.onDone({ data: params, type: 'done' });
      // 回调onCancel
      _instance.options.onCancel && _instance.options.onCancel({ data: params, type: 'cancel' });
      // 回调onClose
      _instance.options.onClose &&
        _instance.options.onClose({ data: params, type: 'config-close' });

      delete unref(instanceMap)[instance.key!];
    }
  }

  function getTemplateItems(template) {
    return Array.isArray(template) ? template : [template];
  }

  function onDone(_: DynamicDrawerInstance, index: number) {
    const currentContentRef: DynamicContentExposeMethods = unref(contentCompRefs)[index];
    if (currentContentRef) {
      const { onDone } = currentContentRef;
      typeof onDone === 'function' && onDone();
    }
  }

  function onCancel(_: DynamicDrawerInstance, index: number) {
    const currentContentRef: DynamicContentExposeMethods = unref(contentCompRefs)[index];
    if (currentContentRef) {
      const { onCancel } = currentContentRef;
      typeof onCancel === 'function' && onCancel();
    }
  }

  function onClose(e: any, instance: DynamicDrawerInstance) {
    closeListener({ instance, params: e });
  }

  DynamicDrawerEmitter.on('open', openListener);
  DynamicDrawerEmitter.on('close', closeListener);
  DynamicDrawerEmitter.on('close-all', () => (instanceMap.value = {}));
</script>

<template>
  <template v-for="(instance, _, instanceIndex) in instanceMap" :key="instance.key">
    <div>{{ instance }}</div>
    <BasicDrawer
      v-bind="instance.options.props"
      :open="instance.open"
      :_instance="instance"
      :destroy-on-close="true"
      @ok="onDone(instance, instanceIndex)"
      @cancel="onCancel(instance, instanceIndex)"
      @close="(e) => onClose(e, instance)"
    >
      <template
        v-if="instance.options.templates && instance.options.templates.titleToolbar"
        #titleToolbar
      >
        <component
          v-for="(titleToolbar, index) in getTemplateItems(instance.options.templates.titleToolbar)"
          :is="titleToolbar"
          :key="index + '_title_toolbar'"
          v-bind="instance.options.emits"
        />
      </template>
      <template v-if="instance.options.templates && instance.options.templates.title" #title>
        <component
          v-for="(title, index) in getTemplateItems(instance.options.templates.title)"
          :is="title"
          :key="index + '_title'"
          v-bind="instance.options.emits"
        />
      </template>
      <component :is="instance.content" v-bind="instance.options.emits" ref="contentCompRefs" />
      <template
        v-if="instance.options.templates && instance.options.templates.insertFooter"
        #insertFooter
      >
        <component
          v-for="(insertFooter, index) in getTemplateItems(instance.options.templates.insertFooter)"
          :is="insertFooter"
          :key="index + '_insert_footer'"
          v-bind="instance.options.emits"
        />
      </template>
      <template
        v-if="instance.options.templates && instance.options.templates.centerFooter"
        #centerFooter
      >
        <component
          v-for="(centerFooter, index) in getTemplateItems(instance.options.templates.centerFooter)"
          :is="centerFooter"
          :key="index + '_center_footer'"
          v-bind="instance.options.emits"
        />
      </template>
      <template
        v-if="instance.options.templates && instance.options.templates.appendFooter"
        #appendFooter
      >
        <component
          v-for="(appendFooter, index) in getTemplateItems(instance.options.templates.appendFooter)"
          :is="appendFooter"
          :key="index + '_append_footer'"
          v-bind="instance.options.emits"
        />
      </template>
      <template v-if="instance.options.templates && instance.options.templates.footer" #footer>
        <component
          v-for="(footer, index) in getTemplateItems(instance.options.templates.footer)"
          :is="footer"
          :key="index + '_footer'"
          v-bind="instance.options.emits"
        />
      </template>
    </BasicDrawer>
  </template>
</template>
