{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"baseUrl": ".", "declaration": false, "target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "module": "ESNext", "moduleResolution": "node", "strict": true, "noImplicitOverride": true, "noUnusedLocals": true, "esModuleInterop": true, "useUnknownInCatchVariables": false, "composite": false, "declarationMap": false, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "skipLibCheck": true, "noImplicitAny": false, "noUnusedParameters": false, "preserveWatchOutput": true, "experimentalDecorators": true, "resolveJsonModule": true, "removeComments": true, "types": ["vite/client", "@types/node"], "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}