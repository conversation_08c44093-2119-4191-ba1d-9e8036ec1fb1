<template>
  <div class="content">
    <iframe
      class="ifram"
      src="http://************:8718/yuweiplayer_301/lives.html?plateNo=苏CPZ353&channel=1,2,3,4"
      frameborder="0"
    ></iframe>
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive } from 'vue';
  import { useRoute } from 'vue-router';

  export default defineComponent({
    name: 'Video',
    components: {},
    setup() {
      const { params } = useRoute();
      console.log(params.channelIds);
      const channelIds = params.channelIds.split(',');

      return {
        channelIds,
      };
    },
  });
</script>
<style scoped>
  .content {
    overflow: hidden;
  }

  .video {
    width: calc(50% - 10px);
    height: 400px;
    margin: 5px;
    padding: 20px;
    float: left;
  }

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    border: none;
  }
</style>
