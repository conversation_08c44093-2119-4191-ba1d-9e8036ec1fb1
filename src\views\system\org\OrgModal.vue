<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-10 15:39:42
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-18 14:00:21
 * @FilePath     : \tzlink-gps-web\src\views\system\org\OrgModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './org.data';
  import { addOrg, editOrg } from '@/api/passport/org';

  export default defineComponent({
    name: 'OrgModal',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const uid = ref<number | undefined>(undefined);
      const [registerForm, { resetFields, setFieldsValue, validate, validateFields }] = useForm({
        labelWidth: 100,
        schemas: formSchema,
        showActionButtonGroup: false,
      });

      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        resetFields();
        setModalProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;
        uid.value = data.record?.uid;
        if (unref(isUpdate)) {
          setFieldsValue({
            ...data.record,
          });
        }
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

      async function handleSubmit() {
        try {
          // TODO custom api
          let values = await validate();
          values.uid = uid.value;
          if (unref(isUpdate)) {
            setModalProps({ confirmLoading: true });
            console.log(values, unref(isUpdate), JSON.stringify(values));
            await editOrg(uid.value, values);
          } else {
            setModalProps({ confirmLoading: true });
            console.log(values, unref(isUpdate), JSON.stringify(values));
            await addOrg(values);
          }
          closeModal();
          emit('success', {
            isUpdate: unref(isUpdate),
            values: { ...values, uid: uid.value },
          });
        } catch (e: any) {
          console.log('error', e, e.response);
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return { registerModal, registerForm, getTitle, handleSubmit };
    },
  });
</script>
