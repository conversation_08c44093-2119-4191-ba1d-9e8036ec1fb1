# FROM node:20-alpine as builder

# WORKDIR /usr/app
# COPY package.json /usr/app
# RUN npm install --registry=http://192.168.1.163:8081/repository/npm-public/

# ADD . /usr/app

# RUN npm run build

# FROM nginx:alpine
# RUN rm -rf /etc/localtime && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' > /etc/timezone
# COPY --from=builder /usr/app/dist/ /usr/share/nginx/html/
# COPY --from=builder /usr/app/nginx.conf /etc/nginx/nginx.conf

# CMD ["/bin/sh", "-c", "sed -i \"s@%PROXY%@$GATEWAY_URL@\" /etc/nginx/nginx.conf; nginx -g \"daemon off;\""]

FROM nginx:alpine
RUN rm -rf /etc/localtime && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' > /etc/timezone
COPY ./dist/ /usr/share/nginx/html/
COPY ./nginx.conf /etc/nginx/nginx.conf

CMD ["/bin/sh", "-c", "sed -i \"s@%PROXY%@$GATEWAY_URL@\" /etc/nginx/nginx.conf; nginx -g \"daemon off;\""]
