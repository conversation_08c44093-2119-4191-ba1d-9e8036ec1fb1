import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 设备列表
 */
export function getTelecontrolEquipList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/telecontrol/equips`,
    data,
  });
}

/**
 * @description: 获取设备远程控制命令
 * @param equipUid 设备uid
 */
export function getTelecontrolCommands(equipUid) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/telecontrol/${equipUid}/commands`,
  });
}

/**
 * @description: 获取设备远程控制命令
 * @param equipUid 设备uid
 */
export function sendTelecontrolCommand(equipUid, data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/telecontrol/${equipUid}/send`,
    data,
  });
}
