<template>
  <div class="relative !h-full w-full overflow-hidden" ref="el"></div>
</template>

<script lang="ts" setup>
  import { type PropType, ref, onMounted, onUnmounted, watch } from 'vue';
  import type { Nullable } from '@/types';
  import { MODE } from './../typing';
  import { basicSetup } from 'codemirror';
  import { EditorView, keymap, ViewUpdate } from '@codemirror/view';
  import { EditorState, Compartment } from '@codemirror/state';
  import { javascript } from '@codemirror/lang-javascript';
  import { json } from '@codemirror/lang-json';
  import { html } from '@codemirror/lang-html';
  import { StreamLanguage } from '@codemirror/language';
  import { yaml } from '@codemirror/legacy-modes/mode/yaml';
  import { standardKeymap, insertTab } from '@codemirror/commands';
  import { autocompletion } from '@codemirror/autocomplete';
  import { oneDark } from '@codemirror/theme-one-dark';
  import { watchEffect } from 'vue';

  const props = defineProps({
    mode: {
      type: String as PropType<MODE>,
      default: MODE.JSON,
      validator(value: any) {
        // 这个值必须匹配下列字符串中的一个
        return Object.values(MODE).includes(value);
      },
    },
    value: { type: String, default: '' },
    editable: { type: Boolean, default: false },
  });

  const emit = defineEmits(['change']);

  const el = ref();
  let editor: Nullable<EditorView>;

  watch(
    () => props.value,
    (value) => {
      const oldValue = editor?.state.doc.toString();
      if (value !== oldValue) {
        changeEditorContent(value);
      }
    },
    { flush: 'post' },
  );

  watchEffect(() => {
    changeEditable(props.editable);
  });

  watchEffect(() => {
    changeMode(props.mode);
  });

  function changeEditorContent(doc: string) {
    editor?.dispatch({ changes: { from: 0, to: editor.state.doc.length, insert: doc } });
  }

  function changeEditable(editable: boolean) {
    editor?.dispatch({
      effects: editableCompartment.reconfigure(EditorView.editable.of(editable)),
    });
  }

  function changeMode(mode: MODE) {
    editor?.dispatch({
      effects: languageCompartment.reconfigure([getLanguageSupport(mode)]),
    });
  }

  function getLanguageSupport(mode: MODE) {
    if (mode === MODE.HTML) {
      return html();
    }

    if (mode === MODE.JS) {
      return javascript();
    }

    if (mode === MODE.YAML) {
      return StreamLanguage.define(yaml);
    }

    return json();
  }

  function handleEditorDocChange(v: ViewUpdate) {
    if (v.docChanged) {
      emit('change', v.state.doc.toString());
    }
  }

  const editableCompartment = new Compartment();
  const languageCompartment = new Compartment();
  // 实例化编辑器
  async function init() {
    const tabSize = new Compartment();
    const fixedHeightTheme = EditorView.theme({
      '&': { height: `${window.innerHeight - 145}px` },
      '.cm-scroller': { overflow: 'auto' },
    });

    const startState = EditorState.create({
      //doc为编辑器内容
      extensions: [
        basicSetup,
        tabSize.of(EditorState.tabSize.of(2)),
        keymap.of([
          ...standardKeymap,
          // Tab Keymap
          {
            key: 'Tab',
            run: insertTab,
          },
        ]),
        oneDark,
        fixedHeightTheme,
        autocompletion({ activateOnTyping: true }),
        editableCompartment.of(EditorView.editable.of(false)),
        EditorView.updateListener.of(handleEditorDocChange),
        languageCompartment.of([getLanguageSupport(props.mode)]),
      ],
    });

    editor = new EditorView({
      state: startState,
      parent: el.value,
    });
  }

  onMounted(() => {
    init();
  });

  onUnmounted(() => {
    editor?.destroy();
    editor = null;
  });
</script>
