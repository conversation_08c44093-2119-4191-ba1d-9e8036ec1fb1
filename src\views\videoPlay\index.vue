<template>
  <div class="content">
    <div class="video" v-for="(item, index) in channelIds" :key="index">
      <video-player
        ref="videoPlayer"
        :volume="0.6"
        controls
        :height="320"
        :src="
          'http://************:8718/yuweiplayer_301/lives.html?plateNo=苏C26208&channel=' +
          channelIds[index]
        "
        :options="playerOptions"
      />
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive } from 'vue';
  import { useRoute } from 'vue-router';

  export default defineComponent({
    name: 'Video',
    components: {},
    setup() {
      const { params } = useRoute();
      console.log(params.channelIds);
      const channelIds = params.channelIds.split(',');
      const playerOptions = reactive({
        playbackRates: [0.5, 1.0, 1.5, 2.0], //可选择的播放速度
        autoplay: true, //如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        height: 200,
        aspectRatio: '30:14', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        // sources: [
        //   {
        //     type: 'rtmp/flv', //video/webm
        //     src: 'http://************:8718/yuweiplayer_301/lives.html?plateNo=苏C26208&channel=1', //url地址
        //   },
        // ],
        poster: '', //你的封面地址
        // width: document.documentElement.clientWidth,
        //notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true, //当前时间和持续时间的分隔符
          durationDisplay: true, //显示持续时间
          remainingTimeDisplay: false, //是否显示剩余时间功能
          fullscreenToggle: true, //全屏按钮
        },
      });
      return {
        playerOptions,
        channelIds,
      };
    },
  });
</script>
<style scoped>
  .content {
    overflow: hidden;
  }

  .video {
    width: calc(50% - 10px);
    height: 400px;
    margin: 5px;
    padding: 20px;
    float: left;
  }
</style>
