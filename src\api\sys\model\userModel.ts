/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  loginName: string;
  password: string;
  application?: string;
  ip?: string;
}

// export interface RoleInfo {
//   roleName: string;
//   value: string;
// }
export type RoleInfo = string;

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  authorizations: RoleInfo[];
  name: string;
  token: string;
  uid: string | number;
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  roles: RoleInfo[];
  // 用户id
  userId: string | number;
  // 用户名
  username: string;
  // 真实名字
  realName: string;
  // 头像
  avatar: string;
  // 介绍
  desc?: string;
}
