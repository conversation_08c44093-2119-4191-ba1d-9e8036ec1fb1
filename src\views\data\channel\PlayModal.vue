<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './role.data';
  import { addchannel, editchannel } from '@/api/data/channel';

  const playerOptions = reactive({
    playbackRates: [0.5, 1.0, 1.5, 2.0], //可选择的播放速度
    autoplay: true, //如果true,浏览器准备好时开始回放。
    muted: false, // 默认情况下将会消除任何音频。
    loop: false, // 视频一结束就重新开始。
    preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
    language: 'zh-CN',
    aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
    sources: [
      {
        type: 'video/mp4',
        src: 'https://cdn.jsdelivr.net/gh/xdlumia/files/video-play/IronMan.mp4', //url地址
      },
    ],
    poster: '', //你的封面地址
    // width: document.documentElement.clientWidth,
    notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    controlBar: {
      timeDivider: true, //当前时间和持续时间的分隔符
      durationDisplay: true, //显示持续时间
      remainingTimeDisplay: false, //是否显示剩余时间功能
      fullscreenToggle: true, //全屏按钮
    },
  });

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  const getTitle = computed(() => '播放视频');

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      // TODO custom api
      console.log(values);
      if (unref(isUpdate)) {
        await editchannel(rowId.value, values);
      } else {
        await addchannel(values);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <!-- <BasicForm @register="registerForm" /> -->
    <div id="player">
      <video-player ref="videoPlayer" controls :options="playerOptions" />
    </div>
  </BasicModal>
</template>
<style scoped>
  #player {
    width: 800px;
    height: 500px;
    margin: 0 auto;
  }
</style>
