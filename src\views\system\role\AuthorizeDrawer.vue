<template>
  <BasicDrawer
    v-bind="$attrs"
    show-footer
    :title="getTitle"
    width="50%"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <Tree
      v-if="treeData?.length"
      v-model:checkedKeys="checkedKeys"
      :tree-data="treeData"
      :field-names="{ title: 'name', key: 'id' }"
      check-strictly
      checkable
      toolbar
      default-expand-all
      title="菜单分配"
    />
  </BasicDrawer>
</template>
<script lang="ts">
  import type { TreeProps } from 'ant-design-vue';

  import { defineComponent, ref } from 'vue';
  import { Tree } from 'ant-design-vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';

  import { getAssignableResources } from '@/api/passport/resource';
  import { getRoleAuthorize, setRoleAuthorize } from '@/api/passport/role';

  import { useUserStoreWithOut } from '@/store/modules/user';

  export default defineComponent({
    name: 'AuthorizeDrawer',
    components: { BasicDrawer, Tree },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const id = ref<number | undefined>(undefined);
      const checkedKeys = ref<{
        checked: string[] | number[];
        halfChecked: string[] | number[];
      }>({ checked: [], halfChecked: [] });
      const treeData = ref<TreeProps['treeData']>([]);

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        setDrawerProps({ confirmLoading: false });
        id.value = data?.record?.id;
        console.log('id', id.value);
        treeData.value = await getAssignableResources(useUserStoreWithOut().getUserInfo.uid);
        if (id.value) {
          getRoleAuthorize(id.value).then((res) => {
            checkedKeys.value = res;
          });
        }
      });

      const getTitle = '角色权限设置';

      async function handleSubmit() {
        try {
          setDrawerProps({ confirmLoading: true });
          try {
            await setRoleAuthorize(id.value, checkedKeys.value?.checked);
            closeDrawer();
            emit('success');
          } catch (error) {
            console.log('error', error);
          }
        } finally {
          setDrawerProps({ confirmLoading: false });
        }
      }

      return { registerDrawer, checkedKeys, getTitle, handleSubmit, treeData };
    },
  });
</script>
