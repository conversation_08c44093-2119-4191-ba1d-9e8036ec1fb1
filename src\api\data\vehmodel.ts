import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';
/**
 * @description: 车型列表
 */
export function getvehicleModelList(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/vehicleModel/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建车型
 */
export function addDeviceModel(data) {
  return request.post({
    url: `${MicroServiceEnum.MONITOR}/vehicleModel`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑车型
 */
export function editDeviceModel(id, data) {
  return request.put({
    url: `${MicroServiceEnum.MONITOR}/vehicleModel/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}


/**
 * @description: 删除车型
 */
export function removevehicleModel(id) {
  return request.delete({
    url: `${MicroServiceEnum.MONITOR}/vehicleModel/${id}`,
    // permission: 'vendor-remove'
  });
}


/**
 * @description: 获取车型树
 */
export function getvehicleModelTreeOptions() {
  return request.get({
    url: `${MicroServiceEnum.MONITOR}/vehicleModel/tree/options`,
  });
}
