/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-24 15:38:32
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-25 10:47:30
 * @FilePath     : \special-front\src\api\data\protocol.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 控制命令表单数据
 */
export function getProtocolOptions() {
  return defHttp.get({
    url: `/protocol/options`,
  });
}

/**
 * @description: 控制命令表单数据
 */
export function getProtocolCommandOptions(protocol) {
  return defHttp.get({
    url: `/protocol/${protocol}/commands`,
  });
}
