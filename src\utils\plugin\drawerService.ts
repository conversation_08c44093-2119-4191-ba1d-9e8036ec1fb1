import type { DrawerServiceContext } from '@/hooks/component/useDrawer';

import DynamicDrawerEmitter from '@/components/Drawer/src/emitter';
import { DynamicDrawerSymbol } from '@/hooks/component/useDrawer';
import { markRaw } from 'vue';
import { uniqueId } from 'lodash-es';

import type { App, Plugin } from 'vue';
import type { DynamicDrawerInstance, DynamicDrawerOptions } from '@/components/Drawer/src/typing';

export default {
  install: (app: App) => {
    const drawerService = {
      open: (content: any, options?: DynamicDrawerOptions) => {
        const instance: DynamicDrawerInstance = {
          open: false,
          key: `dynamic_drawer_instance_${uniqueId()}`,
          content: content && markRaw(content),
          options: options || {},
          data: options && options.data,
          close: (params) => {
            DynamicDrawerEmitter.emit('close', { instance, params });
          },
        };

        DynamicDrawerEmitter.emit('open', { instance });

        return instance;
      },
      closeAll: () => {
        DynamicDrawerEmitter.emit('close-all');
      },
    };

    // app.config.unwrapInjectedRef = true; // Remove it after Vue 3.3. Details: https://vuejs.org/guide/components/provide-inject.html#working-with-reactivity
    app.config.globalProperties.$drawer = drawerService;
    app.provide<DrawerServiceContext>(DynamicDrawerSymbol, drawerService);
  },
} as Plugin;
