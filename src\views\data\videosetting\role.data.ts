import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '供应商',
    dataIndex: 'vendorName',
  },
  {
    title: '基础路径',
    dataIndex: 'baseUrl',
  },
  {
    title: '名称',
    dataIndex: 'name',
  },

  {
    title: 'secret',
    dataIndex: 'secret',
  },
  {
    title: 'secretKey',
    dataIndex: 'secretKey',
  },
  {
    title: '类型',
    dataIndex: 'type',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.type === 'Frame' ? 'blue' : 'green',
        },
        {
          default: () => (record.type === 'Frame' ? '内嵌frame' : '推流'),
        },
      );
    },
  },
];

export const searchFormSchema: FormSchema[] = [];

export const formSchema: FormSchema[] = [];
