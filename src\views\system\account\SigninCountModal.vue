<script lang="ts" setup name="member-sign-in-modal">
import type { BasicColumn } from '@/components/Table'
import { BasicTable, useTable } from '@/components/Table'
import { BasicModal, useModalInner } from '@/components/Modal'
import { getSignInLogList } from '@/api/passport/member'
import { ref } from 'vue'
import { omit } from 'lodash-es'
// register modal
// const [registerModal, { setModalProps }] = useModalInner(async () => {
//   setModalProps({ confirmLoading: false })
// })
const cuid = ref<any>()
const [registerModal, { setModalProps }] = useModalInner(async data => {
  setModalProps({ confirmLoading: false })
  console.log(data.record)
  cuid.value = data.record.uid
  reload()
})
const signInLogColumns: BasicColumn[] = [
  {
    title: '登录时间',
    dataIndex: 'signinTime'
  },
  {
    title: '登录IP',
    dataIndex: 'ip'
  }
]
const [registerTable, { reload }] = useTable({
  api: getSignInLogList,
  tableSetting: {
    size: false
  },
  canResize: false,
  rowKey: 'id',
  columns: signInLogColumns,
  useSearchForm: false,
  showTableSetting: true,
  bordered: true,
  beforeFetch: async (searchInfo: any) => {
    console.log('before fetch : ', searchInfo)
    if (!searchInfo) return
    if (!searchInfo.criteria) searchInfo.criteria = {}
    const info = omit(searchInfo, [
      'pageIndex',
      'pageSize',
      'criteria',
      'order'
    ])
    for (const key of Object.keys(info)) {
      searchInfo.criteria[key] = info[key]
      delete searchInfo[key]
    }
    console.log(cuid)
    searchInfo.criteria.cuid = cuid
    // searchInfo.criteria.application = route.query.application
    console.log('searchInfo : ', JSON.stringify(searchInfo))
    return searchInfo
  },
  fetchSetting: {
    listField: 'data'
  }
})
</script>

<template>
  <BasicModal v-bind="$attrs" title="登录日志" @register="registerModal">
    <template #footer></template>
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>
