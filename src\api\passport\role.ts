/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-20 15:05:05
 * @FilePath     : \special-front\src\api\passport\role.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp as request } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';
/**
 * @description: 角色列表
 */
export function getRoleList(data) {
  return request.post({
    url: `/role/query`,
    data,
    // permission: 'role-add'
  });
}

/**
 * @description: 新建角色
 */
export function addRole(data) {
  return request.post({
    url: `/role`,
    data,
    // permission: 'role-add'
  });
}

/**
 * @description: 编辑角色
 */
export function editRole(id, data) {
  return request.put({
    url: `/role/${id}`,
    data,
    // permission: 'role-edit'
  });
}

/**
 * @description: 删除角色
 */
export function removeRole(id) {
  return request.delete({
    url: `/role/${id}`,
    // permission: 'role-remove'
  });
}

/**
 * @description: 获取角色可访问的资源
 */
export function getRoleAuthorize(id) {
  return request.get({
    url: `/role/${id}/authorize`,
    method: 'get',
    // permission: 'NONE'
  });
}

/**
 * @description: 编辑角色可访问的资源
 */
export function setRoleAuthorize(id, data) {
  return request.put({
    url: `/role/${id}/authorize`,
    method: 'put',
    data,
    // permission: 'role-authorize'
  });
}

/**
 * @description: 获取角色下拉框
 */
export function getRoleOptions() {
  return request.get({
    url: `/role/options`,
    // permission: 'NONE'
  });
}

export function getMemberType() {
  return request.get({
    url: `/enums/member-type`,
    // permission: 'NONE'
  });
}
