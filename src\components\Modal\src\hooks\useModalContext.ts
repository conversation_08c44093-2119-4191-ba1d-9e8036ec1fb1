import { InjectionKey } from 'vue';
import { createContext, useContext } from '@/hooks/core/useContext';
import { DynamicModalContext } from '../typing';

export interface ModalContextProps {
  redoModalHeight: () => void;
}

const key: InjectionKey<ModalContextProps> = Symbol();

export const ModalContextKey: InjectionKey<DynamicModalContext> = Symbol();

export function createModalContext(context: ModalContextProps) {
  return createContext<ModalContextProps>(context, key);
}

export function useModalContext() {
  return useContext<ModalContextProps>(key);
}

export function useDynamicModalContext() {
  return useContext<DynamicModalContext>(ModalContextKey);
}
