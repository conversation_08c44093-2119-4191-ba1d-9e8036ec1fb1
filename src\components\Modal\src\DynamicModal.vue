<script setup lang="ts">
  import type {
    DynamicModalEmitterOpenOptions,
    DynamicModalEmitterCloseOptions,
    DynamicModalInstance,
    DynamicContentExposeMethods,
  } from './typing';

  import { ref, unref } from 'vue';
  import { BasicModal } from '@/components/Modal';
  import DynamicModalEmitter from './emitter';

  defineOptions({
    name: 'DynamicModal',
    inheritAttrs: false,
  });

  const instanceMap = ref<Recordable<DynamicModalInstance>>({});

  const currentInstance = ref<DynamicModalInstance | undefined>(undefined);

  const contentCompRefs = ref<DynamicContentExposeMethods[]>([]);

  function openListener({ instance }: DynamicModalEmitterOpenOptions): void {
    instance.open = true;
    unref(instanceMap)[instance.key] = instance;
    currentInstance.value = instance;
  }

  function closeListener({ instance, params }: DynamicModalEmitterCloseOptions): void {
    const _instance: DynamicModalInstance = unref(instanceMap)[instance.key!];
    console.log('close listener ~~~');

    if (_instance) {
      _instance.open = false;
      // 回调onDone
      _instance.options.onDone && _instance.options.onDone({ data: params, type: 'done' });
      // 回调onCancel
      _instance.options.onCancel && _instance.options.onCancel({ data: params, type: 'cancel' });
      // 回调onClose
      _instance.options.onClose &&
        _instance.options.onClose({ data: params, type: 'config-close' });

      currentInstance.value = _instance;
    }
  }

  function getTemplateItems(template) {
    return Array.isArray(template) ? template : [template];
  }

  function onDone(_: DynamicModalInstance, index: number) {
    const currentContentRef: DynamicContentExposeMethods = unref(contentCompRefs)[index];
    console.log(
      'currentContentRef : ',
      index,
      currentContentRef,
      unref(contentCompRefs),
      instanceMap,
    );
    if (currentContentRef) {
      const { onDone } = currentContentRef;
      typeof onDone === 'function' && onDone();
    }
    console.log('on done emit : ', _);
  }

  function onCancel(_: DynamicModalInstance, index: number) {
    const currentContentRef: DynamicContentExposeMethods = unref(contentCompRefs)[index];
    if (currentContentRef) {
      const { onCancel } = currentContentRef;
      typeof onCancel === 'function' && onCancel();
    }
    console.log('on cancel emit : ', _);
  }

  function handleAfterClose() {
    console.log('handle after close : ', currentInstance.value);
    if (currentInstance.value) {
      delete unref(instanceMap)[currentInstance.value.key!];
      currentInstance.value = undefined;
    }
  }

  DynamicModalEmitter.on('open', openListener);
  DynamicModalEmitter.on('close', closeListener);
  DynamicModalEmitter.on('close-all', () => (instanceMap.value = {}));
</script>

<template>
  <template v-for="(instance, _, instanceIndex) in instanceMap" :key="instance.key">
    <BasicModal
      v-model:open="instance.open"
      v-bind="instance.options.props"
      :_instance="instance"
      :destroy-on-close="true"
      :after-close="handleAfterClose"
      @ok="onDone(instance, instanceIndex)"
      @cancel="onCancel(instance, instanceIndex)"
    >
      <template v-if="instance.options.templates && instance.options.templates.header" #header>
        <component
          v-for="(header, index) in getTemplateItems(instance.options.templates.header)"
          :is="header"
          :key="index + '_header'"
          v-bind="instance.options.emits"
        />
      </template>
      <component :is="instance.content" v-bind="instance.options.emits" ref="contentCompRefs" />
      <template v-if="instance.options.templates && instance.options.templates.footer" #footer>
        <component
          v-for="(footer, index) in getTemplateItems(instance.options.templates.footer)"
          :is="footer"
          :key="index + '_footer'"
          v-bind="instance.options.emits"
        />
      </template>
    </BasicModal>
  </template>
</template>
