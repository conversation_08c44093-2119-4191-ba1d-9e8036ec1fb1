import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 控制命令表单数据
 */
export function getProtocolOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/protocol/options`,
  });
}

/**
 * @description: 控制命令表单数据
 */
export function getProtocolCommandOptions(protocol) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/protocol/${protocol}/commands`,
  });
}
