import { ClassComponent, GlobalComponentConstructor } from '#/ts-helper';
import { DynamicModalOptions, DynamicModalInstance } from '@/components/Modal/src/typing';
import { DynamicDrawerOptions, DynamicDrawerInstance } from '@/components/Drawer/src/typing';

export interface DynamicModalProps {}

export interface DynamicModalEmits {}

export interface DynamicModalSlots {}

declare class DynamicModal extends ClassComponent<
  DynamicModalProps,
  DynamicModalSlots,
  DynamicModalEmits
> {}

export interface ModalServiceMethods {
  /**
   * Displays the modal using the dynamic modal object options.
   * @param {*} content - Dynamic component for content template
   * @param {DynamicModalOptions} options - DynamicModal Object
   * @return {@link DynamicModalInstance}
   */
  open(content: any, options?: DynamicModalOptions): DynamicModalInstance;
  /**
   * close all dynamic modal
   */
  closeAll(): void;
}

export interface DynamicDrawerProps {}

export interface DynamicDrawerEmits {}

export interface DynamicDrawerSlots {}

declare class DynamicDrawer extends ClassComponent<
  DynamicDrawerProps,
  DynamicDrawerSlots,
  DynamicDrawerEmits
> {}

export interface DrawerServiceMethods {
  /**
   * Displays the modal using the dynamic modal object options.
   * @param {*} content - Dynamic component for content template
   * @param {DynamicDrawerOptions} options - DynamicDrawer Object
   * @return {@link DynamicDrawerInstance}
   */
  open(content: any, options?: DynamicDrawerOptions): DynamicDrawerInstance;
  /**
   * close all dynamic modal
   */
  closeAll(): void;
}

declare module '@vue/runtime-core' {
  interface GlobalComponents {
    DynamicModal: GlobalComponentConstructor<DynamicModal>;
    DynamicDrawer: GlobalComponentConstructor<DynamicDrawer>;
  }
}

declare module 'vue/types/vue' {
  interface Vue {
    $modal: ModalServiceMethods;
    $drawer: DrawerServiceMethods;
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $modal: ModalServiceMethods;
    $drawer: DrawerServiceMethods;
  }
}
