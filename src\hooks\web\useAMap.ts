import { ref, unref, onMounted, onUnmounted } from 'vue';

export type AMapPoint = [number, number] | AMap.LngLat;

/**
 * @description 高德地图hook，便于管理地图加载、创建和销毁。
 * @param {AMap.MapOptions} options 高德地图实例化参数
 */
export function useAMap(options: AMap.MapOptions, callback?: (map: AMap.Map) => void) {
  const map = ref<AMap.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();

  // dom挂载完成后初始化map实例
  onMounted(() => {
    console.log('map ref : ', unref(mapRef));
    if (unref(mapRef)) {
      map.value = new AMap.Map(unref(mapRef)!, options);
      if (typeof callback === 'function') callback(unref(map)!);
    }
  });

  function init() {
    map.value = new AMap.Map(unref(mapRef)!, options);
    if (typeof callback === 'function') callback(unref(map)!);
  }

  function destroy() {
    map.value?.destroy();

  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    map.value?.destroy();
  });

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   * @returns {AMap.Map} （map）地图实例
   */
  function setMapCenter(lngLat: [number, number]) {
    if (unref(map)) {
      unref(map)?.setCenter(lngLat);
    }
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   * @returns {AMap.Map} （map）地图实例
   */
  function setMapCenterFitView(lngLat: [number, number]) {
    if (unref(map)) {
      unref(map)?.setCenter(lngLat);
      unref(map)?.setFitView();
    }
  }

  function addMarker(position: AMapPoint, options: AMap.MarkerOptions = {}) {
    const marker = new AMap.Marker({
      position, // 基点位置
      ...options,
    });
    unref(map)?.add(marker);

    return marker;
  }

  function addPolyline(points: AMapPoint[]) {
    const polyline = new AMap.Polyline({
      path: points,
      borderWeight: 10,
      strokeColor: '#4d78bc',
      // strokeOpacity: 0.5
    });

    unref(map)?.add(polyline);
    console.log('unref(map)', unref(map));
    console.log('polyline', polyline);

    return polyline;
  }

  function getMapInstance() {
    return unref(map);
  }

  function clearMap() {
    unref(map)?.clearMap();
  }

  function getMapBoundary() {
    if (!unref(map)) return {};
    const bounds = unref(map)!.getBounds();

    return {
      maxLat: bounds.northEast.lat,
      maxLng: bounds.northEast.lng,
      minLat: bounds.southWest.lat,
      minLng: bounds.southWest.lng,
    };
  }

  const geocoder = new (AMap as any).Geocoder({
    city: '全国', // 城市设为北京，默认：“全国”
    radius: 1000, // 范围，默认：500
  });

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: AMapPoint): Promise<string> {
    return new Promise((resolve, reject) => {
      geocoder.getAddress(new AMap.LngLat(lngLat[0], lngLat[1]), (status, result) => {
        if (status === 'complete' && result.regeocode) {
          resolve(result.regeocode.formattedAddress);
        } else {
          reject({ status, result });
        }
      });
    });
  }

  function createInfoWindow() {
    const infoWindow = new AMap.InfoWindow({
      anchor: 'bottom-center',
      map: unref(map),
    });

    return infoWindow;
  }

  return {
    mapRef,
    map,
    setMapCenter,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
  };
}

/**
 * @description 获取高德地图geocoder
 */
export function useGeoCoder() {
  const geocoder = new (AMap as any).Geocoder({
    city: '全国', // 城市设为北京，默认：“全国”
    radius: 1000, // 范围，默认：500
  });

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]) {
    return new Promise<string>((resolve, reject) => {
      geocoder.getAddress(new AMap.LngLat(lngLat[0], lngLat[1]), (status, result) => {
        if (status === 'complete' && result.regeocode) {
          resolve(result.regeocode.formattedAddress);
        } else if (status === 'no_data') {
          resolve('');
        } else {
          reject({ status, result });
        }
      });
    });
  }
  return {
    geocoder,
    getAddress,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => void;
