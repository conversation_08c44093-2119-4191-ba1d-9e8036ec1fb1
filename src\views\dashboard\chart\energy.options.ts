import * as echarts from 'echarts';

export default {
  grid: {
    top: '40',
    left: '15',
    right: '20',
    bottom: '15',
    containLabel: true,
  },
  legend: {
    textStyle: {
      color: '#ccc',
    },
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        // 坐标轴轴线相关设置。数学上的x轴
        show: true,
        lineStyle: {
          color: '#233653',
        },
      },
      axisLabel: {
        // 坐标轴刻度标签的相关设置
        textStyle: {
          color: '#7ec7ff',
          padding: 8,
          fontSize: 14,
        },
        formatter(data) {
          return data;
        },
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: '#192a44',
        },
      },
      axisTick: {
        show: false,
      },
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
    },
  ],
  yAxis: [
    {
      // name: "人数",
      nameTextStyle: {
        color: '#7ec7ff',
        fontSize: 16,
        padding: 10,
      },
      min: 0,
      splitLine: {
        show: false,
        lineStyle: {
          color: '#192a44',
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#233653',
        },
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#7ec7ff',
          padding: 5,
        },
        formatter(value) {
          if (value === 0) {
            return value;
          }
          return value;
        },
      },
      axisTick: {
        show: false,
      },
      minInterval: 1,
    },
  ],
  series: [
    {
      name: '工时',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: true,
      lineStyle: {
        normal: {
          width: 3,
          color: 'rgba(25,163,223,1)', // 线条颜色
        },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: 'rgba(25,163,223,1)',
        borderColor: '#646ace',
        borderWidth: 2,
      },
      tooltip: {
        show: true,
      },
      areaStyle: {
        // 区域填充样式
        normal: {
          // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(25,163,223,.6)',
              },
              {
                offset: 1,
                color: 'rgba(25,163,223, 0)',
              },
            ],
            false,
          ),
          shadowColor: 'rgba(25,163,223, 0.8)', // 阴影颜色
          shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        },
      },
      data: [1600, 4000, 2000, 300, 800, 1200, 2300, 1100, 3000],
    },
    {
      name: '油耗',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: true,
      lineStyle: {
        normal: {
          width: 3,
          color: 'rgba(49,215,169,1)', // 线条颜色
        },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: 'rgba(49,215,169,1)',
        borderColor: '#646ace',
        borderWidth: 2,
      },
      tooltip: {
        show: true,
      },
      areaStyle: {
        // 区域填充样式
        normal: {
          // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(49,215,169,.6)',
              },
              {
                offset: 1,
                color: 'rgba(49,215,169, 0)',
              },
            ],
            false,
          ),
          shadowColor: 'rgba(49,215,169, 0.8)', // 阴影颜色
          shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        },
      },
      data: [0, 3000, 4000, 1200, 3200, 1200, 2800, 1500, 4300],
    },
  ],
};
