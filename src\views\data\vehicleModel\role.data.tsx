/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:05
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-24 15:11:24
 * @FilePath     : \special-front\src\views\data\vehicleModel\role.data.tsx
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { BasicColumn, FormSchema } from '@/components/Table';
import { getvehicleModelTreeOptions } from '@/api/data/vehmodel';
import { getOrgTreeOptions } from '@/api/passport/org';

export const columns: BasicColumn[] = [
  {
    title: '车型名称',
    dataIndex: 'modelName',
    width: 280,
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
    width: 280,
  },
  {
    title: '车型大类',
    dataIndex: 'parentName',
    width: 280,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 280,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'modelName',
    label: '车型名称',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'orgId',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'modelName',
    label: '车型名称',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
  {
    field: 'parentId',
    label: '车型大类',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: {
      api: getvehicleModelTreeOptions,
      labelField: 'modelName',
      valueField: 'id',
      getPopupContainer: () => document.body,
    },
    required: false,
  },
];
