<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { getFenceCarList, removeFenceCar } from '@/api/vehicle/fence';
  import { getvehicleList } from '@/api/vehicle/vehlist';
  import car from '../../../assets/images/equip/car.png';

  const emit = defineEmits(['success']);

  let params = ref({
    fenceId: 0,
    keyword: '',
  });
  const dataSource = ref([]);
  const fenceDetail: any = ref(null);
  let markers: any = [];
  const selectedKeys = ref<any>([]);

  let map: any = ref(null);
  const mapContainerRef = ref<HTMLElement | null>(null);

  // 复用相同图标
  const CAR_ICON = new AMap.Icon({
    image: car,
    imageSize: new AMap.Size(30, 30),
    size: new AMap.Size(30, 30),
    offset: new AMap.Pixel(-15, -15),
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    markers = [];
    dataSource.value = [];
    selectedKeys.value = [];
    // 销毁地图实例
    if (map.value) {
      map.value.destroy();
      map.value = null;
    }
    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    params.value.fenceId = data.item.id;
    params.value.keyword = '';
    fenceDetail.value = data.item;

    // 初始化地图
    if (mapContainerRef.value) {
      map.value = new AMap.Map(mapContainerRef.value, {
        center: [116.33, 39.9],
        zoom: 5,
        mapStyle: 'amap://styles/darkblue',
      });

      // 地图加载完成后，调用 initMap 绘制图形和加载插件
      map.value.on('complete', async () => {
        await initMap();
        await reload();
      });
    }
  });

  function initMap() {
    const { fenceJson, fenceType, districtCodes = '' } = fenceDetail.value;
    let json = fenceJson ? JSON.parse(fenceJson) : [];
    if (!map.value) return;
    map.value.clearMap();
    // 根据数据显示已保存的围栏
    if (json.length && fenceType === 'POLYGON') {
      let polygon = new AMap.Polygon({
        path: json,
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
        strokeOpacity: 1,
        fillOpacity: 0.5,
        strokeWeight: 1,
        strokeDasharray: [5, 5],
      });
      map.value.add(polygon);
      map.value.setFitView([polygon]);
    } else if (json.length && fenceType === 'CIRCULAR') {
      let circleRadius = json.filter((v) => typeof v === 'number');
      let circle = new AMap.Circle({
        center: json[0],
        radius: circleRadius[0] || 0,
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
      });
      map.value.add(circle);
      map.value.setFitView([circle]);
    } else if (fenceType === 'AREA' && districtCodes) {
      drawDistricts(districtCodes);
    }
  }

  function drawDistricts(districtCodes: any) {
    if (!map.value) return;
    map.value.clearMap();
    if (!districtCodes) return;
    let codesArray = Array.isArray(districtCodes) ? districtCodes : districtCodes.split(',');

    if (codesArray.length) {
      AMap.plugin('AMap.DistrictSearch', function () {
        let districtSearch = new AMap.DistrictSearch({
          level: 'district',
          subdistrict: 0,
          extensions: 'all',
        });
        codesArray.forEach((code) => {
          districtSearch.search(code, function (status, result) {
            if (status === 'complete' && result.info === 'OK') {
              let bounds = result.districtList[0].boundaries;
              if (bounds) {
                let polygons = [];
                for (let i = 0; i < bounds.length; i++) {
                  polygons.push(
                    new AMap.Polygon({
                      map: map.value,
                      path: bounds[i],
                      strokeWeight: 2,
                      strokeOpacity: 0.5,
                      strokeColor: '#527fff',
                      fillColor: '#2b866d',
                      fillOpacity: 0.35,
                    }),
                  );
                }
                map.value.setFitView(polygons);
              }
            }
          });
        });
      });
    }
  }

  function batchGetAddress(data) {
    return new Promise((resolve, reject) => {
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder();
        var point = [data.lng, data.lat]; // 注意：高德地图的坐标顺序是先经度后纬度
        console.log('ponit', point)
        geocoder.getAddress(point, function (status, result) {
          if (status === 'complete' && result.regeocode) {
            data.address = result.regeocode.formattedAddress;
            resolve(data);
          } else {
            console.error('根据经纬度查询地址失败：', result);
            reject(result);
          }
        });
      });
    });
  }

  // 创建标记函数
  const createMarker = (item: any, map: AMap.Map) => {
    if (!item.lng || !item.lat) return null;

    const marker = new AMap.Marker({
      position: new AMap.LngLat(item.lng, item.lat),
      icon: CAR_ICON,
      anchor: 'center',
      extData: item,
    });

    map.add(marker);
    return marker;
  };

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '所属机构',
      dataIndex: 'orgName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '定位时间',
      dataIndex: 'gpsTime',
    },
    {
      title: '定位位置',
      dataIndex: 'address',
    },
  ];

  const [registerTable, { reload, getDataSource }] = useTable({
    api: getvehicleList,
    immediate: false,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.keyword',
          component: 'Input',
          label: '关键字',
          colProps: { span: 9 },
        },
      ],
    },
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    rowSelection: {
      type: 'radio',
      hideSelectAll: true,
      selectedRowKeys: selectedKeys,
      onChange: (selectedRowKeys: any) => {
        const marker = markers.find((m) => m.getExtData().id === selectedRowKeys[0]);
        if (marker) {
          map.value.setCenter(marker.getPosition());
        }
      },
    },
    // actionColumn: {
    //   width: 120,
    //   title: '操作',
    //   dataIndex: 'action',
    //   slot: 'action',
    //   fixed: undefined,
    // },
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria }) => {
      criteria.fenceId = params.value.fenceId;
    },
    afterFetch: (data) => {
      const validMarkers = data
        .map((item) => createMarker(item, map.value))
        .filter(Boolean) as AMap.Marker[];

      markers.push(...validMarkers);

      return Promise.all(
        data.map(async (item) =>
          item.lng && item.lat
            ? batchGetAddress(item).then((res) => ({ ...item, address: res.address }))
            : item,
        ),
      );
    },
  });

  // const handleDelete = async (record) => {
  //   await removeFenceCar(record.deviceId);
  // };

  const tableRef = ref<Nullable<TableActionType>>(null);
</script>

<template>
  <BasicModal width="1200px" v-bind="$attrs" title="关联设备" @register="registerModal">
    <BasicTable @register="registerTable" ref="tableRef">
      <template #aboveOfTable>
        <div class="mapRef" ref="mapContainerRef"></div>
      </template>
      <template #action="{ record }">
        <!-- <TableAction
          :actions="[
            {
              icon: 'flowbite:link-break-outline',
              color: 'error',
              popConfirm: {
                title: '是否确认解绑该设备',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        /> -->
      </template>
    </BasicTable>
  </BasicModal>
</template>

<style lang="less" scoped>
  .mapRef {
    width: 100%;
    height: 500px;
  }
</style>
