<template>
  <div>
    <div class="h-10 w-full p-2">
      <Tabs v-model:activeKey="activeName" @change="handleTabChange">
        <TabPane tab="实时报警" key="current" />
        <TabPane tab="历史报警" key="history" />
      </Tabs>
    </div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <TableAction
        :actions="[
          // {
          //   icon: 'flowbite:link-break-outline',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认解绑该设备',
          //     confirm: handleDelete.bind(null, record),
          //   },
          // },
        ]"
      />
    </BasicTable>

    <AlarmHistory @register="registerModal" />
  </div>
</template>
<script lang="tsx" setup>
  import { ref } from 'vue';
  import { Tabs, TabPane, Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getFenceAlarmList } from '@/api/vehicle/fence';
  import { useModal } from '@/components/Modal';
  import AlarmHistory from './alarmHistory.vue';

  const activeName = ref('current');
  const [registerModal, { openModal }] = useModal();

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '报警类型',
      dataIndex: 'alertEvent',
      customRender: ({ record }) => {
        return record.alertEvent === 'OUT' ? (
          <Tag color={'orange'}>驶出</Tag>
        ) : (
          <Tag color={'green'}>驶入</Tag>
        );
      },
    },
    {
      title: '报警围栏',
      dataIndex: 'fenceName',
    },
    {
      title: '开始时间',
      dataIndex: 'alertStart',
    },
    {
      title: '结束时间',
      dataIndex: 'alertEnd',
    },
    {
      title: '持续时间(h)',
      dataIndex: 'continueTime',
    },
    {
      title: '位置',
      dataIndex: 'address',
      width: 250,
    },
    {
      title: '越界历史',
      dataIndex: 'historyCount',
      fixed: 'right',
      customRender: ({ record }) => {
        return (
          <a href="javascript:void(0)" onClick={handleHistory.bind(null, record)}>
            {record.historyCount}
          </a>
        );
      },
      ifShow: () => activeName.value === 'current',
    },
  ];

  function batchGetAddress(data) {
    return new Promise((resolve, reject) => {
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder();
        var point = [data.wgs84Lat, data.wgs84Lng]; // 注意：高德地图的坐标顺序是先经度后纬度
        console.log('point', point);

        geocoder.getAddress(point, function (status, result) {
          if (status === 'complete' && result.regeocode) {
            data.address = result.regeocode.formattedAddress;
            resolve(data);
          } else {
            console.error('根据经纬度查询地址失败：', result);
            reject(result);
          }
        });
      });
    });
  }

  // tabs切换
  const handleTabChange = (activeKey) => {
    reload();
  };

  const handleHistory = (record) => {
    openModal(true, {
      isActual: 2,
      ...record,
    });
  };

  const [registerTable, { reload, getDataSource }] = useTable({
    api: getFenceAlarmList,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.keyword',
          component: 'Input',
          label: '关键字',
          colProps: { span: 9 },
        },
        {
          field: 'criteria.dataRange',
          component: 'RangePicker',
          label: '时间范围',
          colProps: { span: 9 },
          componentProps: {
            format: 'YYYY-MM-DD HH:mm:ss',
            showTime: true,
          },
        },
      ],
    },
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      slot: 'action',
      fixed: 'right',
    },
    beforeFetch: ({ criteria }) => {
      if (activeName.value === 'current') {
        criteria.isActual = 1;
      } else {
        criteria.isActual = 2;
      }
      if (criteria.dataRange && criteria.dataRange.length) {
        criteria.alertDateTimeRange = {
          beginTime: criteria.dataRange[0],
          endTime: criteria.dataRange[1],
        };
        delete criteria.dataRange;
      }
    },
    afterFetch: (data) => {
      return Promise.all(
        data.map(async (item) =>
          item.wgs84Lng && item.wgs84Lat
            ? batchGetAddress(item).then((res) => ({ ...item, address: res.address }))
            : item,
        ),
      );
    },
  });
</script>
