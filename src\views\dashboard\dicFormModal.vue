<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-26 17:34:28
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-26 18:52:55
 * @FilePath     : \special-front\src\views\dashboard\dicFormModal.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { getAllTerminal, setTerminal } from '@/api/data/command';
  import { useMessage } from '@/hooks/web/useMessage';

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);
  const detail = ref();
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    changeVal.value = '';
    activeItem.value = {};
    detail.value = data?.record || {};
    const res = await getAllTerminal({ terminalId: detail.value.deviceSn });
    const { json } = res;
    dicList.value.forEach((v) => {
      v.value = json[String(v.key)];
    });
  });

  const dicList = ref([
    {
      key: '1',
      title: '心跳间隔',
      unit: '秒',
      value: '',
    },
    {
      key: '13',
      value: '',
      title: '服务器IP',
    },
    {
      key: '18',
      value: '',
      title: '端口号',
    },
    {
      key: '27',
      unit: '秒',
      value: '',
      title: '休眠时间',
    },
    {
      key: '29',
      unit: '秒',
      value: '',
      title: '汇报间隔',
    },
    {
      key: '55',
      value: '',
      unit: 'km/h',
      title: '最高速度',
    },
    {
      key: 'f000',
      value: '',
      title: '蓝牙脉冲系数',
    },
  ]);

  const activeItem: any = ref({});
  const changeVal = ref('');
  function changeDic(item) {
    activeItem.value = item;
    changeVal.value = item.value;
  }
  const { createMessage } = useMessage();

  async function handleSubmit() {
    try {
      if (!changeVal.value) {
        return createMessage.warning('请输入参数值');
      }
      const res = await setTerminal({
        terminalId: detail.value.deviceSn,
        params: JSON.stringify({ [activeItem.value.key]: changeVal.value }),
      });
      console.log(res);
      createMessage.success(res?.json?.notice || '成功');
      closeModal();
      emit('success');
      // const values = await validate();
      // setModalProps({ confirmLoading: true });
      // TODO custom api
      // if (unref(isUpdate)) {
      //   await editdriver(rowId.value, values);
      // } else {
      //   await adddriver(values);
      // }
      // closeModal();
      // emit('success', {
      //   isUpdate: unref(isUpdate),
      //   values: { ...values, id: rowId.value },
      // });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal
    width="800px"
    v-bind="$attrs"
    title="常用命令"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <div class="container">
      <div class="left">
        <div class="list">
          <div
            class="item"
            @click="changeDic(item)"
            :class="{ active: activeItem && activeItem.key == item.key }"
            v-for="item in dicList"
            :key="item.key"
            >{{ item.title }}</div
          >
        </div>
      </div>
      <div class="right" v-if="detail">
        <div class="item">
          <div class="label">车架号：</div>
          <div class="value">{{ detail.vin }}</div>
        </div>
        <div class="item">
          <div class="label">车牌号：</div>
          <div class="value">{{ detail.license }}</div>
        </div>
        <div class="item">
          <div class="label">协议类型：</div>
          <div class="value">JT808</div>
        </div>
        <div class="item" v-if="activeItem.key">
          <div class="label">{{ activeItem.title }}</div>
          <div class="value">
            <a-input v-model:value="changeVal" placeholder="请输入" />
            <span v-if="activeItem.unit">{{ activeItem.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<style scoped lang="less">
  .container {
    display: flex;
    align-items: center;
    gap: 20px;

    .left {
      flex-shrink: 0;
      width: 200px;
      height: 300px;
      overflow-y: auto;
          border-radius:5px;
      color:#fff;

      .list {
        .item {
          box-sizing: border-box;
          width: 100%;
          margin:0 0 5px;
          padding: 5px 24px;
          cursor: pointer;
        }

        .item:hover {
          border-radius:5px;
          background: #5dcafb;
        }

        .item.active {
          border-radius:5px;
          background: #5dcafb;
        }
      }
    }

    .right {
      box-sizing: border-box;
      flex: 1;
      width: 100px;
      height: 300px;
      padding: 12px;
          border-radius:5px;
      background:#025F8B;
      color:#fff;

      .item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .label,
        .value {
          display: flex;
          align-items: center;
          height: 32px;
          line-height: 32px;
          gap: 10px;
        }

        .label {
          width: 100px;
        }
      }
    }
  }
</style>
