/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-25 14:28:16
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-29 15:39:43
 * @FilePath     : \special-front\src\views\dashboard\index.data.tsx
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const carColumns: BasicColumn[] = [
  {
    title: '车架号',
    dataIndex: 'vin',
    width: 200,
  },
  {
    title: '车牌号',
    dataIndex: 'license',
  },
  // {
  //   title: '发动机号',
  //   dataIndex: 'engineNo',
  // },
  {
    title: '协议类型',
    dataIndex: 'protocol',
  },
  {
    title: '设备序号',
    dataIndex: 'deviceSn',
  },
  {
    title: 'SIM卡号',
    dataIndex: 'simNo',
  },
  {
    title: '在线状态',
    dataIndex: 'online',
    align: 'center',

    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.online == '在线' ? 'green' : 'blue',
        },
        {
          default: () => record.online,
        },
      );
    },
  },
  {
    title: 'gps时间',
    dataIndex: 'gpsTime',
    width: 200,
  },
  {
    title: '网关接受数据时间',
    dataIndex: 'gatewayTime',
    width: 200,
  },
  {
    title: '定位状态',
    dataIndex: 'location',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.location == '有效定位' ? 'green' : 'blue',
        },
        {
          default: () => record.location || '-',
        },
      );
    },
  },
  {
    title: 'ACC状态',
    dataIndex: 'acc',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.acc == '开' ? 'green' : 'red',
        },
        {
          default: () => record.acc || '-',
        },
      );
    },
  },
  {
    title: '锁车状态',
    dataIndex: 'lock',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.lockStatus == '未锁' ? 'green' : 'red',
        },
        {
          default: () => record.lockStatus || '-',
        },
      );
    },
  },
  {
    title: 'ACC开机时长',
    dataIndex: 'accHour',
  },
  {
    title: '发动机工作时长',
    dataIndex: 'workHour',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];
