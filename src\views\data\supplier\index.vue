<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增供应商 </a-button>
      </template>
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer" @click="handleSettingVideo(record)">{{
          record.settingCount
        }}</Tag>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getVendorList, removeVendor } from '@/api/data/supplier';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import AddFormModal from './AddFormModal.vue';

  import { useDrawer } from '@/components/Drawer';

  import { columns, searchFormSchema } from './role.data.tsx';

  export default defineComponent({
    name: 'role-list',
    components: { BasicTable, TableAction, Tag, AddFormModal },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: '供应商列表',
        api: getVendorList,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: undefined,
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerSettingVideo, { openModal: openSettingVideoModal }] = useModal();

      const [registerDrawer, { openDrawer }] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeVendor(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        registerDrawer,
      };
    },
  });
</script>
