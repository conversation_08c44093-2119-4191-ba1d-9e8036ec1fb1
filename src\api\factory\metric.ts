import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

export enum MetricKind {
  SUM = 'SUM',
  GAUGE = 'GAUGE',
}

export enum MetricValueType {
  STRING = 'STRING',
  UBYTE = 'UBYTE',
  BYTE = 'BYTE',
  SHORT = 'SHORT',
  USHORT = 'USHORT',
  INT = 'INT',
  UINT = 'UINT',
  LONG = 'LONG',
  DECIMAL = 'DECIMAL',
}

/**
 * @description: 工况列表
 */
export function getMetricList(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/metric/query`,
    data,
  });
}

/**
 * @description: 新建工况
 */
export function addMetric(data) {
  return defHttp.post({
    url: `${MicroServiceEnum.FACTORY}/metric`,
    data,
  });
}

/**
 * @description: 编辑工况
 */
export function editMetric(id, data) {
  return defHttp.put({
    url: `${MicroServiceEnum.FACTORY}/metric/${id}`,
    data,
  });
}

/**
 * @description: 删除工况
 */
export function removeMetric(id) {
  return defHttp.delete({
    url: `${MicroServiceEnum.FACTORY}/metric/${id}`,
  });
}

/**
 * @description: 工况表单数据
 */
export function getMetricDetail(id) {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metric/${id}`,
  });
}

/**
 * @description: 获取工况数据类型
 */
export function getMetricValueTypeOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metric/value-type/options`,
  });
}

/**
 * @description: 获取工况类型
 */
export function getMetricKindOptions() {
  return defHttp.get({
    url: `${MicroServiceEnum.FACTORY}/metric/kind/options`,
  });
}
