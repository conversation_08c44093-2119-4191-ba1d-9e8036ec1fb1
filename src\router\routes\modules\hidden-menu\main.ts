import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const permission: AppRouteModule = {
  path: '/hidden-menu',
  name: 'HiddenMenu',
  component: LAYOUT,
  meta: {
    orderNo: 9000,
    icon: 'ion:build-outline',
    title: '隐藏菜单',
    hidden: true,
  },
  children: [
    // {
    //   path: '/equip/single/:equipUid',
    //   name: 'SingleEquipmentInfo',
    //   meta: {
    //     title: '设备信息',
    //   },
    //   component: () => import('@/views/data/equip/single.vue'),
    // },
    {
      path: '/data/videosetting/:vendorId',
      name: 'videosetting',
      meta: {
        title: '视频配置',
      },
      component: () => import('@/views/data/videosetting/index.vue'),
    },
    {
      path: '/data/channel/:id',
      name: 'channel',
      meta: {
        title: '通道信息',
      },
      component: () => import('@/views/data/channel/index.vue'),
    },
    {
      path: '/dashboard/:vehicleId/:type',
      name: 'monitor',
      meta: {
        title: '车辆监控',
      },
      component: () => import('@/views/dashboard/index.vue'),
    },
    {
      path: '/singleVideo/:vehicleId/:channelNo',
      name: 'singleVideo',
      meta: {
        title: `通道视频`,
      },
      component: () => import('@/views/data/channel/singleVideo.vue'),
    },
    {
      path: '/easyplay/:id',
      name: 'easyplay',
      meta: {
        title: '播放视频流',
      },
      component: () => import('@/views/videoPlay/easyplay.vue'),
    },
  ],
};

export default permission;
